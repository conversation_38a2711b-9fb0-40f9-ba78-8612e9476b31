{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/SubscriptionManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { HyperliquidSubscription, ConnectionStatus } from '@/types/hyperliquid';\nimport { Plus, Trash2, Play, Pause } from 'lucide-react';\n\ninterface SubscriptionManagerProps {\n  subscriptions: HyperliquidSubscription[];\n  onSubscribe: (subscription: HyperliquidSubscription) => string;\n  onUnsubscribe: (subscriptionId: string) => void;\n  connectionStatus: ConnectionStatus;\n}\n\nconst POPULAR_SYMBOLS = ['HYPER', 'BTC', 'ETH', 'SOL', 'AVAX', 'MATIC', 'DOGE', 'ADA', 'DOT'];\nconst SUBSCRIPTION_TYPES = [\n  { value: 'trades', label: 'Trades', description: 'Real-time trade data' },\n  { value: 'l2Book', label: 'Order Book', description: 'Level 2 order book updates' },\n  { value: 'candle', label: 'Candles', description: 'OHLCV candle data' },\n  { value: 'webData2', label: 'Web Data', description: 'General market data' },\n  { value: 'notification', label: 'Notifications', description: 'User notifications' },\n  { value: 'activeAssetCtx', label: 'Asset Context', description: 'Active asset context' }\n];\n\nconst CANDLE_INTERVALS = ['1m', '5m', '15m', '1h', '4h', '1d'];\n\nexport function SubscriptionManager({\n  subscriptions,\n  onSubscribe,\n  onUnsubscribe,\n  connectionStatus\n}: SubscriptionManagerProps) {\n  const [newSubscription, setNewSubscription] = useState<Partial<HyperliquidSubscription>>({\n    type: 'trades',\n    symbol: 'HYPER'\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  const handleAddSubscription = () => {\n    if (newSubscription.type) {\n      try {\n        const subscription: HyperliquidSubscription = {\n          type: newSubscription.type,\n          ...(newSubscription.symbol && { symbol: newSubscription.symbol }),\n          ...(newSubscription.coin && { coin: newSubscription.coin }),\n          ...(newSubscription.interval && { interval: newSubscription.interval }),\n          ...(newSubscription.user && { user: newSubscription.user })\n        };\n\n        onSubscribe(subscription);\n        setNewSubscription({ type: 'trades', symbol: 'HYPER' });\n        setShowAddForm(false);\n      } catch (error) {\n        console.error('Error adding subscription:', error);\n        alert('Error adding subscription. Please try again.');\n      }\n    }\n  };\n\n  const isConnected = connectionStatus === 'connected';\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            Subscription Manager\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Manage your Hyperliquid WebSocket subscriptions\n          </p>\n        </div>\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          disabled={!isConnected}\n          className={`flex items-center px-4 py-2 rounded-lg transition-colors ${\n            isConnected\n              ? 'bg-blue-600 text-white hover:bg-blue-700'\n              : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n          }`}\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Subscription\n        </button>\n      </div>\n\n      {/* Connection Status Warning */}\n      {!isConnected && (\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <Pause className=\"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2\" />\n            <p className=\"text-yellow-800 dark:text-yellow-200\">\n              WebSocket is not connected. Connect to manage subscriptions.\n            </p>\n          </div>\n        </div>\n      )}\n\n      {/* Add Subscription Form */}\n      {showAddForm && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border\">\n          <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n            Add New Subscription\n          </h3>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* Subscription Type */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Subscription Type\n              </label>\n              <select\n                value={newSubscription.type || ''}\n                onChange={(e) => setNewSubscription(prev => ({\n                  ...prev,\n                  type: e.target.value as HyperliquidSubscription['type']\n                }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                {SUBSCRIPTION_TYPES.map(type => (\n                  <option key={type.value} value={type.value}>\n                    {type.label} - {type.description}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Symbol/Coin */}\n            {(newSubscription.type === 'trades' || newSubscription.type === 'l2Book' || newSubscription.type === 'candle') && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Symbol\n                </label>\n                <div className=\"flex space-x-2\">\n                  <select\n                    value={newSubscription.symbol || ''}\n                    onChange={(e) => setNewSubscription(prev => ({ ...prev, symbol: e.target.value }))}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    {POPULAR_SYMBOLS.map(symbol => (\n                      <option key={symbol} value={symbol}>{symbol}</option>\n                    ))}\n                  </select>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Custom\"\n                    value={newSubscription.symbol || ''}\n                    onChange={(e) => setNewSubscription(prev => ({ ...prev, symbol: e.target.value }))}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Interval for candles */}\n            {newSubscription.type === 'candle' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Interval\n                </label>\n                <select\n                  value={newSubscription.interval || '1m'}\n                  onChange={(e) => setNewSubscription(prev => ({ ...prev, interval: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  {CANDLE_INTERVALS.map(interval => (\n                    <option key={interval} value={interval}>{interval}</option>\n                  ))}\n                </select>\n              </div>\n            )}\n\n            {/* User for notifications */}\n            {newSubscription.type === 'notification' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  User Address\n                </label>\n                <input\n                  type=\"text\"\n                  placeholder=\"0x...\"\n                  value={newSubscription.user || ''}\n                  onChange={(e) => setNewSubscription(prev => ({ ...prev, user: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex justify-end space-x-3 mt-6\">\n            <button\n              onClick={() => setShowAddForm(false)}\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleAddSubscription}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Add Subscription\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Active Subscriptions */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Active Subscriptions ({subscriptions.length})\n          </h3>\n        </div>\n\n        <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n          {subscriptions.length === 0 ? (\n            <div className=\"px-6 py-8 text-center\">\n              <Play className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                No active subscriptions. Add one to get started.\n              </p>\n            </div>\n          ) : (\n            subscriptions.map((subscription, index) => (\n              <div key={index} className=\"px-6 py-4 flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                      {subscription.type}\n                    </span>\n                    {subscription.symbol && (\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {subscription.symbol}\n                      </span>\n                    )}\n                    {subscription.coin && (\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {subscription.coin}\n                      </span>\n                    )}\n                    {subscription.interval && (\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {subscription.interval}\n                      </span>\n                    )}\n                    {subscription.user && (\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400 font-mono\">\n                        {subscription.user.slice(0, 8)}...\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => {\n                    try {\n                      onUnsubscribe(`sub_${index}`);\n                    } catch (error) {\n                      console.error('Error unsubscribing:', error);\n                    }\n                  }}\n                  disabled={!isConnected}\n                  className={`p-2 rounded-lg transition-colors ${\n                    isConnected\n                      ? 'text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20'\n                      : 'text-gray-400 cursor-not-allowed'\n                  }`}\n                  title=\"Remove subscription\"\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </button>\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAJA;;;;AAaA,MAAM,kBAAkB;IAAC;IAAS;IAAO;IAAO;IAAO;IAAQ;IAAS;IAAQ;IAAO;CAAM;AAC7F,MAAM,qBAAqB;IACzB;QAAE,OAAO;QAAU,OAAO;QAAU,aAAa;IAAuB;IACxE;QAAE,OAAO;QAAU,OAAO;QAAc,aAAa;IAA6B;IAClF;QAAE,OAAO;QAAU,OAAO;QAAW,aAAa;IAAoB;IACtE;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAAsB;IAC3E;QAAE,OAAO;QAAgB,OAAO;QAAiB,aAAa;IAAqB;IACnF;QAAE,OAAO;QAAkB,OAAO;QAAiB,aAAa;IAAuB;CACxF;AAED,MAAM,mBAAmB;IAAC;IAAM;IAAM;IAAO;IAAM;IAAM;CAAK;AAEvD,SAAS,oBAAoB,EAClC,aAAa,EACb,WAAW,EACX,aAAa,EACb,gBAAgB,EACS;IACzB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;QACvF,MAAM;QACN,QAAQ;IACV;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,wBAAwB;QAC5B,IAAI,gBAAgB,IAAI,EAAE;YACxB,IAAI;gBACF,MAAM,eAAwC;oBAC5C,MAAM,gBAAgB,IAAI;oBAC1B,GAAI,gBAAgB,MAAM,IAAI;wBAAE,QAAQ,gBAAgB,MAAM;oBAAC,CAAC;oBAChE,GAAI,gBAAgB,IAAI,IAAI;wBAAE,MAAM,gBAAgB,IAAI;oBAAC,CAAC;oBAC1D,GAAI,gBAAgB,QAAQ,IAAI;wBAAE,UAAU,gBAAgB,QAAQ;oBAAC,CAAC;oBACtE,GAAI,gBAAgB,IAAI,IAAI;wBAAE,MAAM,gBAAgB,IAAI;oBAAC,CAAC;gBAC5D;gBAEA,YAAY;gBACZ,mBAAmB;oBAAE,MAAM;oBAAU,QAAQ;gBAAQ;gBACrD,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM;YACR;QACF;IACF;IAEA,MAAM,cAAc,qBAAqB;IAEzC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,8OAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,UAAU,CAAC;wBACX,WAAW,CAAC,yDAAyD,EACnE,cACI,6CACA,gDACJ;;0CAEF,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,CAAC,6BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAE,WAAU;sCAAuC;;;;;;;;;;;;;;;;;YAQzD,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,gBAAgB,IAAI,IAAI;wCAC/B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oDAC3C,GAAG,IAAI;oDACP,MAAM,EAAE,MAAM,CAAC,KAAK;gDACtB,CAAC;wCACD,WAAU;kDAET,mBAAmB,GAAG,CAAC,CAAA,qBACtB,8OAAC;gDAAwB,OAAO,KAAK,KAAK;;oDACvC,KAAK,KAAK;oDAAC;oDAAI,KAAK,WAAW;;+CADrB,KAAK,KAAK;;;;;;;;;;;;;;;;4BAQ5B,CAAC,gBAAgB,IAAI,KAAK,YAAY,gBAAgB,IAAI,KAAK,YAAY,gBAAgB,IAAI,KAAK,QAAQ,mBAC3G,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO,gBAAgB,MAAM,IAAI;gDACjC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAChF,WAAU;0DAET,gBAAgB,GAAG,CAAC,CAAA,uBACnB,8OAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;0DAGjB,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,gBAAgB,MAAM,IAAI;gDACjC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAChF,WAAU;;;;;;;;;;;;;;;;;;4BAOjB,gBAAgB,IAAI,KAAK,0BACxB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,gBAAgB,QAAQ,IAAI;wCACnC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAClF,WAAU;kDAET,iBAAiB,GAAG,CAAC,CAAA,yBACpB,8OAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;;;;;;4BAOpB,gBAAgB,IAAI,KAAK,gCACxB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO,gBAAgB,IAAI,IAAI;wCAC/B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,WAAU;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAAsD;gCAC3C,cAAc,MAAM;gCAAC;;;;;;;;;;;;kCAIhD,8OAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;mCAKlD,cAAc,GAAG,CAAC,CAAC,cAAc,sBAC/B,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,aAAa,IAAI;;;;;;gDAEnB,aAAa,MAAM,kBAClB,8OAAC;oDAAK,WAAU;8DACb,aAAa,MAAM;;;;;;gDAGvB,aAAa,IAAI,kBAChB,8OAAC;oDAAK,WAAU;8DACb,aAAa,IAAI;;;;;;gDAGrB,aAAa,QAAQ,kBACpB,8OAAC;oDAAK,WAAU;8DACb,aAAa,QAAQ;;;;;;gDAGzB,aAAa,IAAI,kBAChB,8OAAC;oDAAK,WAAU;;wDACb,aAAa,IAAI,CAAC,KAAK,CAAC,GAAG;wDAAG;;;;;;;;;;;;;;;;;;kDAMvC,8OAAC;wCACC,SAAS;4CACP,IAAI;gDACF,cAAc,CAAC,IAAI,EAAE,OAAO;4CAC9B,EAAE,OAAO,OAAO;gDACd,QAAQ,KAAK,CAAC,wBAAwB;4CACxC;wCACF;wCACA,UAAU,CAAC;wCACX,WAAW,CAAC,iCAAiC,EAC3C,cACI,0DACA,oCACJ;wCACF,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;+BA7CZ;;;;;;;;;;;;;;;;;;;;;;AAsDxB"}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/PriceChart.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\nimport { PriceData } from '@/types/hyperliquid';\nimport { TrendingUp, TrendingDown, Activity } from 'lucide-react';\n\ninterface PriceChartProps {\n  data: PriceData[];\n}\n\nexport function PriceChart({ data }: PriceChartProps) {\n  const chartData = useMemo(() => {\n    // Group data by symbol and create time series\n    const symbolData = data.reduce((acc, item) => {\n      if (!acc[item.symbol]) {\n        acc[item.symbol] = [];\n      }\n      acc[item.symbol].push({\n        timestamp: item.timestamp,\n        price: item.price,\n        time: new Date(item.timestamp).toLocaleTimeString(),\n        volume: item.volume || 0\n      });\n      return acc;\n    }, {} as Record<string, any[]>);\n\n    // Return the most recent data for the primary symbol (HYPER first, then BTC, then first available)\n    const primarySymbol = data.find(d => d.symbol === 'HYPER')?.symbol ||\n                          data.find(d => d.symbol === 'BTC')?.symbol ||\n                          data[0]?.symbol;\n    if (!primarySymbol || !symbolData[primarySymbol]) return [];\n\n    return symbolData[primarySymbol]\n      .sort((a, b) => a.timestamp - b.timestamp)\n      .slice(-50); // Keep last 50 data points\n  }, [data]);\n\n  const latestData = useMemo(() => {\n    if (data.length === 0) return null;\n\n    // Get latest data for each symbol\n    const latest = data.reduce((acc, item) => {\n      if (!acc[item.symbol] || item.timestamp > acc[item.symbol].timestamp) {\n        acc[item.symbol] = item;\n      }\n      return acc;\n    }, {} as Record<string, PriceData>);\n\n    return Object.values(latest);\n  }, [data]);\n\n  const primarySymbol = latestData?.[0]?.symbol || 'N/A';\n  const primaryPrice = latestData?.[0]?.price || 0;\n  const priceChange = latestData?.[0]?.change24h || 0;\n\n  if (!latestData || latestData.length === 0) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <Activity className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No price data available. Subscribe to trades or candles to see charts.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Price Chart\n          </h3>\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n            Real-time price movements\n          </p>\n        </div>\n\n        {/* Primary Symbol Info */}\n        <div className=\"text-right\">\n          <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            ${primaryPrice.toLocaleString(undefined, {\n              minimumFractionDigits: 2,\n              maximumFractionDigits: 2\n            })}\n          </div>\n          <div className={`flex items-center text-sm ${\n            priceChange >= 0 ? 'text-green-600' : 'text-red-600'\n          }`}>\n            {priceChange >= 0 ? (\n              <TrendingUp className=\"h-4 w-4 mr-1\" />\n            ) : (\n              <TrendingDown className=\"h-4 w-4 mr-1\" />\n            )}\n            {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}%\n          </div>\n        </div>\n      </div>\n\n      {/* Chart */}\n      <div className=\"h-64 mb-6\">\n        {chartData.length > 0 ? (\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <LineChart data={chartData}>\n              <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n              <XAxis\n                dataKey=\"time\"\n                tick={{ fontSize: 12 }}\n                className=\"text-gray-600 dark:text-gray-400\"\n              />\n              <YAxis\n                tick={{ fontSize: 12 }}\n                className=\"text-gray-600 dark:text-gray-400\"\n                domain={['dataMin - 10', 'dataMax + 10']}\n              />\n              <Tooltip\n                contentStyle={{\n                  backgroundColor: 'rgba(0, 0, 0, 0.8)',\n                  border: 'none',\n                  borderRadius: '8px',\n                  color: 'white'\n                }}\n                formatter={(value: number) => [\n                  `$${value.toLocaleString(undefined, {\n                    minimumFractionDigits: 2,\n                    maximumFractionDigits: 2\n                  })}`,\n                  'Price'\n                ]}\n              />\n              <Line\n                type=\"monotone\"\n                dataKey=\"price\"\n                stroke=\"#3B82F6\"\n                strokeWidth={2}\n                dot={false}\n                activeDot={{ r: 4, fill: '#3B82F6' }}\n              />\n            </LineChart>\n          </ResponsiveContainer>\n        ) : (\n          <div className=\"flex items-center justify-center h-full\">\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              Waiting for price data...\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Symbol Grid */}\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n        {latestData.slice(0, 6).map((item) => (\n          <div key={item.symbol} className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {item.symbol}\n              </span>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                {new Date(item.timestamp).toLocaleTimeString()}\n              </span>\n            </div>\n            <div className=\"mt-1\">\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                ${item.price.toLocaleString(undefined, {\n                  minimumFractionDigits: 2,\n                  maximumFractionDigits: 6\n                })}\n              </div>\n              {item.volume && (\n                <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Vol: {item.volume.toLocaleString()}\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAAA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AALA;;;;;AAWO,SAAS,WAAW,EAAE,IAAI,EAAmB;IAClD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,8CAA8C;QAC9C,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,KAAK;YACnC,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,EAAE;gBACrB,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,EAAE;YACvB;YACA,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;gBACpB,WAAW,KAAK,SAAS;gBACzB,OAAO,KAAK,KAAK;gBACjB,MAAM,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;gBACjD,QAAQ,KAAK,MAAM,IAAI;YACzB;YACA,OAAO;QACT,GAAG,CAAC;QAEJ,mGAAmG;QACnG,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,UACtC,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,UACpC,IAAI,CAAC,EAAE,EAAE;QAC/B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,cAAc,EAAE,OAAO,EAAE;QAE3D,OAAO,UAAU,CAAC,cAAc,CAC7B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,CAAC,KAAK,2BAA2B;IAC5C,GAAG;QAAC;KAAK;IAET,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,kCAAkC;QAClC,MAAM,SAAS,KAAK,MAAM,CAAC,CAAC,KAAK;YAC/B,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,SAAS,GAAG,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS,EAAE;gBACpE,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG;YACrB;YACA,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO,OAAO,MAAM,CAAC;IACvB,GAAG;QAAC;KAAK;IAET,MAAM,gBAAgB,YAAY,CAAC,EAAE,EAAE,UAAU;IACjD,MAAM,eAAe,YAAY,CAAC,EAAE,EAAE,SAAS;IAC/C,MAAM,cAAc,YAAY,CAAC,EAAE,EAAE,aAAa;IAElD,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAM1D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAmD;oCAC9D,aAAa,cAAc,CAAC,WAAW;wCACvC,uBAAuB;wCACvB,uBAAuB;oCACzB;;;;;;;0CAEF,8OAAC;gCAAI,WAAW,CAAC,0BAA0B,EACzC,eAAe,IAAI,mBAAmB,gBACtC;;oCACC,eAAe,kBACd,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;6DAEtB,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAEzB,eAAe,IAAI,MAAM;oCAAI,YAAY,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;;0BAM3D,8OAAC;gBAAI,WAAU;0BACZ,UAAU,MAAM,GAAG,kBAClB,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,WAAU;;;;;;0CAC/C,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,MAAM;oCAAE,UAAU;gCAAG;gCACrB,WAAU;;;;;;0CAEZ,8OAAC,qJAAA,CAAA,QAAK;gCACJ,MAAM;oCAAE,UAAU;gCAAG;gCACrB,WAAU;gCACV,QAAQ;oCAAC;oCAAgB;iCAAe;;;;;;0CAE1C,8OAAC,uJAAA,CAAA,UAAO;gCACN,cAAc;oCACZ,iBAAiB;oCACjB,QAAQ;oCACR,cAAc;oCACd,OAAO;gCACT;gCACA,WAAW,CAAC,QAAkB;wCAC5B,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,WAAW;4CAClC,uBAAuB;4CACvB,uBAAuB;wCACzB,IAAI;wCACJ;qCACD;;;;;;0CAEH,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;gCACL,WAAW;oCAAE,GAAG;oCAAG,MAAM;gCAAU;;;;;;;;;;;;;;;;yCAKzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;0BAQtD,8OAAC;gBAAI,WAAU;0BACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC3B,8OAAC;wBAAsB,WAAU;;0CAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,KAAK,MAAM;;;;;;kDAEd,8OAAC;wCAAK,WAAU;kDACb,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0CAGhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAsD;4CACjE,KAAK,KAAK,CAAC,cAAc,CAAC,WAAW;gDACrC,uBAAuB;gDACvB,uBAAuB;4CACzB;;;;;;;oCAED,KAAK,MAAM,kBACV,8OAAC;wCAAI,WAAU;;4CAA2C;4CAClD,KAAK,MAAM,CAAC,cAAc;;;;;;;;;;;;;;uBAlB9B,KAAK,MAAM;;;;;;;;;;;;;;;;AA2B/B"}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/OrderBookDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { OrderBookData, OrderBookLevel } from '@/types/hyperliquid';\nimport { BookOpen, TrendingUp, TrendingDown } from 'lucide-react';\n\ninterface OrderBookDisplayProps {\n  data: OrderBookData | null;\n}\n\ninterface ProcessedLevel extends OrderBookLevel {\n  total: number;\n  percentage: number;\n}\n\nexport function OrderBookDisplay({ data }: OrderBookDisplayProps) {\n  const processedData = useMemo(() => {\n    if (!data) return null;\n\n    // Process bids (buy orders) - sort by price descending\n    const processedBids: ProcessedLevel[] = data.bids\n      .sort((a, b) => b.price - a.price)\n      .slice(0, 15)\n      .reduce((acc, level, index) => {\n        const total = acc.length > 0 ? acc[acc.length - 1].total + level.size : level.size;\n        acc.push({\n          ...level,\n          total,\n          percentage: 0 // Will be calculated after we have max total\n        });\n        return acc;\n      }, [] as ProcessedLevel[]);\n\n    // Process asks (sell orders) - sort by price ascending\n    const processedAsks: ProcessedLevel[] = data.asks\n      .sort((a, b) => a.price - b.price)\n      .slice(0, 15)\n      .reduce((acc, level, index) => {\n        const total = acc.length > 0 ? acc[acc.length - 1].total + level.size : level.size;\n        acc.push({\n          ...level,\n          total,\n          percentage: 0 // Will be calculated after we have max total\n        });\n        return acc;\n      }, [] as ProcessedLevel[]);\n\n    // Calculate percentages\n    const maxBidTotal = Math.max(...processedBids.map(b => b.total), 0);\n    const maxAskTotal = Math.max(...processedAsks.map(a => a.total), 0);\n    const maxTotal = Math.max(maxBidTotal, maxAskTotal);\n\n    processedBids.forEach(bid => {\n      bid.percentage = maxTotal > 0 ? (bid.total / maxTotal) * 100 : 0;\n    });\n\n    processedAsks.forEach(ask => {\n      ask.percentage = maxTotal > 0 ? (ask.total / maxTotal) * 100 : 0;\n    });\n\n    // Calculate spread\n    const bestBid = processedBids[0]?.price || 0;\n    const bestAsk = processedAsks[0]?.price || 0;\n    const spread = bestAsk - bestBid;\n    const spreadPercentage = bestBid > 0 ? (spread / bestBid) * 100 : 0;\n\n    return {\n      bids: processedBids,\n      asks: processedAsks.reverse(), // Reverse to show highest prices at top\n      spread,\n      spreadPercentage,\n      bestBid,\n      bestAsk\n    };\n  }, [data]);\n\n  if (!data || !processedData) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-center h-96\">\n          <div className=\"text-center\">\n            <BookOpen className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No order book data available. Subscribe to l2Book to see order book.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const { bids, asks, spread, spreadPercentage, bestBid, bestAsk } = processedData;\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Order Book - {data.symbol}\n            </h3>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              Level 2 market depth\n            </p>\n          </div>\n          \n          {/* Spread Info */}\n          <div className=\"text-right\">\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">Spread</div>\n            <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              ${spread.toFixed(2)}\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {spreadPercentage.toFixed(3)}%\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Column Headers */}\n        <div className=\"grid grid-cols-3 gap-4 mb-4 text-sm font-medium text-gray-500 dark:text-gray-400\">\n          <div className=\"text-left\">Price</div>\n          <div className=\"text-center\">Size</div>\n          <div className=\"text-right\">Total</div>\n        </div>\n\n        {/* Asks (Sell Orders) */}\n        <div className=\"space-y-1 mb-4\">\n          {asks.map((ask, index) => (\n            <div key={`ask-${index}`} className=\"relative\">\n              {/* Background bar */}\n              <div \n                className=\"absolute inset-y-0 right-0 bg-red-100 dark:bg-red-900/20 rounded\"\n                style={{ width: `${ask.percentage}%` }}\n              />\n              \n              {/* Content */}\n              <div className=\"relative grid grid-cols-3 gap-4 py-1 px-2 text-sm\">\n                <div className=\"text-red-600 dark:text-red-400 font-mono\">\n                  {ask.price.toFixed(2)}\n                </div>\n                <div className=\"text-center text-gray-900 dark:text-white font-mono\">\n                  {ask.size.toFixed(4)}\n                </div>\n                <div className=\"text-right text-gray-600 dark:text-gray-400 font-mono\">\n                  {ask.total.toFixed(4)}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Spread Display */}\n        <div className=\"flex items-center justify-center py-3 my-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center space-x-4\">\n              <div className=\"flex items-center text-green-600 dark:text-green-400\">\n                <TrendingUp className=\"h-4 w-4 mr-1\" />\n                <span className=\"font-mono\">${bestBid.toFixed(2)}</span>\n              </div>\n              <div className=\"text-gray-400\">|</div>\n              <div className=\"flex items-center text-red-600 dark:text-red-400\">\n                <TrendingDown className=\"h-4 w-4 mr-1\" />\n                <span className=\"font-mono\">${bestAsk.toFixed(2)}</span>\n              </div>\n            </div>\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              Spread: ${spread.toFixed(2)} ({spreadPercentage.toFixed(3)}%)\n            </div>\n          </div>\n        </div>\n\n        {/* Bids (Buy Orders) */}\n        <div className=\"space-y-1\">\n          {bids.map((bid, index) => (\n            <div key={`bid-${index}`} className=\"relative\">\n              {/* Background bar */}\n              <div \n                className=\"absolute inset-y-0 right-0 bg-green-100 dark:bg-green-900/20 rounded\"\n                style={{ width: `${bid.percentage}%` }}\n              />\n              \n              {/* Content */}\n              <div className=\"relative grid grid-cols-3 gap-4 py-1 px-2 text-sm\">\n                <div className=\"text-green-600 dark:text-green-400 font-mono\">\n                  {bid.price.toFixed(2)}\n                </div>\n                <div className=\"text-center text-gray-900 dark:text-white font-mono\">\n                  {bid.size.toFixed(4)}\n                </div>\n                <div className=\"text-right text-gray-600 dark:text-gray-400 font-mono\">\n                  {bid.total.toFixed(4)}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Footer Info */}\n        <div className=\"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700\">\n          <div className=\"flex justify-between text-sm text-gray-500 dark:text-gray-400\">\n            <span>Last updated: {new Date(data.timestamp).toLocaleTimeString()}</span>\n            <span>Showing top 15 levels</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAJA;;;;AAeO,SAAS,iBAAiB,EAAE,IAAI,EAAyB;IAC9D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,CAAC,MAAM,OAAO;QAElB,uDAAuD;QACvD,MAAM,gBAAkC,KAAK,IAAI,CAC9C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,IACT,MAAM,CAAC,CAAC,KAAK,OAAO;YACnB,MAAM,QAAQ,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,MAAM,IAAI,GAAG,MAAM,IAAI;YAClF,IAAI,IAAI,CAAC;gBACP,GAAG,KAAK;gBACR;gBACA,YAAY,EAAE,6CAA6C;YAC7D;YACA,OAAO;QACT,GAAG,EAAE;QAEP,uDAAuD;QACvD,MAAM,gBAAkC,KAAK,IAAI,CAC9C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,IACT,MAAM,CAAC,CAAC,KAAK,OAAO;YACnB,MAAM,QAAQ,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,MAAM,IAAI,GAAG,MAAM,IAAI;YAClF,IAAI,IAAI,CAAC;gBACP,GAAG,KAAK;gBACR;gBACA,YAAY,EAAE,6CAA6C;YAC7D;YACA,OAAO;QACT,GAAG,EAAE;QAEP,wBAAwB;QACxB,MAAM,cAAc,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,GAAG;QACjE,MAAM,cAAc,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,GAAG;QACjE,MAAM,WAAW,KAAK,GAAG,CAAC,aAAa;QAEvC,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,UAAU,GAAG,WAAW,IAAI,AAAC,IAAI,KAAK,GAAG,WAAY,MAAM;QACjE;QAEA,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,UAAU,GAAG,WAAW,IAAI,AAAC,IAAI,KAAK,GAAG,WAAY,MAAM;QACjE;QAEA,mBAAmB;QACnB,MAAM,UAAU,aAAa,CAAC,EAAE,EAAE,SAAS;QAC3C,MAAM,UAAU,aAAa,CAAC,EAAE,EAAE,SAAS;QAC3C,MAAM,SAAS,UAAU;QACzB,MAAM,mBAAmB,UAAU,IAAI,AAAC,SAAS,UAAW,MAAM;QAElE,OAAO;YACL,MAAM;YACN,MAAM,cAAc,OAAO;YAC3B;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAK;IAET,IAAI,CAAC,QAAQ,CAAC,eAAe;QAC3B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAAsD;wCACpD,KAAK,MAAM;;;;;;;8CAE3B,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAM1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA2C;;;;;;8CAC1D,8OAAC;oCAAI,WAAU;;wCAAsD;wCACjE,OAAO,OAAO,CAAC;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;wCACZ,iBAAiB,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAY;;;;;;0CAC3B,8OAAC;gCAAI,WAAU;0CAAc;;;;;;0CAC7B,8OAAC;gCAAI,WAAU;0CAAa;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC;gCAAyB,WAAU;;kDAElC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC;wCAAC;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;0DAErB,8OAAC;gDAAI,WAAU;0DACZ,IAAI,IAAI,CAAC,OAAO,CAAC;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;+BAhBf,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;kCAwB5B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAK,WAAU;;wDAAY;wDAAE,QAAQ,OAAO,CAAC;;;;;;;;;;;;;sDAEhD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDAAK,WAAU;;wDAAY;wDAAE,QAAQ,OAAO,CAAC;;;;;;;;;;;;;;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;;wCAAgD;wCACnD,OAAO,OAAO,CAAC;wCAAG;wCAAG,iBAAiB,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;kCAMjE,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC;gCAAyB,WAAU;;kDAElC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC;wCAAC;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;0DAErB,8OAAC;gDAAI,WAAU;0DACZ,IAAI,IAAI,CAAC,OAAO,CAAC;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;+BAhBf,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;kCAwB5B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAK;wCAAe,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;8CAChE,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB"}}, {"offset": {"line": 1445, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/WalletTracker.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport { WalletData, WalletFilter, WalletTrade } from '@/types/hyperliquid';\nimport {\n  Wallet,\n  Filter,\n  TrendingUp,\n  TrendingDown,\n  Clock,\n  DollarSign,\n  Target,\n  Zap,\n  Star,\n  Eye,\n  EyeOff,\n  Plus\n} from 'lucide-react';\n\ninterface WalletTrackerProps {\n  wallets: WalletData[];\n  walletTrades: WalletTrade[];\n  onAddWallet: (address: string, nickname?: string) => void;\n  onRemoveWallet: (address: string) => void;\n  onWalletClick: (wallet: WalletData) => void;\n}\n\nconst DEFAULT_FILTER: WalletFilter = {\n  sortBy: 'pnl24h',\n  sortOrder: 'desc',\n  limit: 10\n};\n\nexport function WalletTracker({\n  wallets,\n  walletTrades,\n  onAddWallet,\n  onRemoveWallet,\n  onWalletClick\n}: WalletTrackerProps) {\n  const [filter, setFilter] = useState<WalletFilter>(DEFAULT_FILTER);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newWalletAddress, setNewWalletAddress] = useState('');\n  const [newWalletNickname, setNewWalletNickname] = useState('');\n  const [watchedWallets, setWatchedWallets] = useState<Set<string>>(new Set());\n\n  const filteredWallets = useMemo(() => {\n    let filtered = wallets.filter(wallet => {\n      if (filter.minVolume24h && wallet.totalVolume24h < filter.minVolume24h) return false;\n      if (filter.maxVolume24h && wallet.totalVolume24h > filter.maxVolume24h) return false;\n      if (filter.minPnl24h && wallet.totalPnl24h < filter.minPnl24h) return false;\n      if (filter.maxPnl24h && wallet.totalPnl24h > filter.maxPnl24h) return false;\n      if (filter.minTradeSize && wallet.avgTradeSize < filter.minTradeSize) return false;\n      if (filter.maxTradeSize && wallet.maxTradeSize > filter.maxTradeSize) return false;\n      if (filter.minWinRate && wallet.winRate < filter.minWinRate) return false;\n      if (filter.maxWinRate && wallet.winRate > filter.maxWinRate) return false;\n      if (filter.onlyScalpers && !wallet.isScalper) return false;\n      if (filter.minScalpingScore && wallet.scalpingScore < filter.minScalpingScore) return false;\n      if (filter.tags && filter.tags.length > 0) {\n        const hasTag = filter.tags.some(tag => wallet.tags.includes(tag));\n        if (!hasTag) return false;\n      }\n      return true;\n    });\n\n    // Sort\n    filtered.sort((a, b) => {\n      let aValue: number, bValue: number;\n      switch (filter.sortBy) {\n        case 'volume24h':\n          aValue = a.totalVolume24h;\n          bValue = b.totalVolume24h;\n          break;\n        case 'pnl24h':\n          aValue = a.totalPnl24h;\n          bValue = b.totalPnl24h;\n          break;\n        case 'pnlAllTime':\n          aValue = a.totalPnlAllTime;\n          bValue = b.totalPnlAllTime;\n          break;\n        case 'winRate':\n          aValue = a.winRate;\n          bValue = b.winRate;\n          break;\n        case 'tradeCount':\n          aValue = a.tradeCount24h;\n          bValue = b.tradeCount24h;\n          break;\n        case 'scalpingScore':\n          aValue = a.scalpingScore;\n          bValue = b.scalpingScore;\n          break;\n        default:\n          aValue = a.totalPnl24h;\n          bValue = b.totalPnl24h;\n      }\n\n      return filter.sortOrder === 'desc' ? bValue - aValue : aValue - bValue;\n    });\n\n    return filtered.slice(0, filter.limit);\n  }, [wallets, filter]);\n\n  const handleAddWallet = () => {\n    if (newWalletAddress.trim()) {\n      onAddWallet(newWalletAddress.trim(), newWalletNickname.trim() || undefined);\n      setNewWalletAddress('');\n      setNewWalletNickname('');\n      setShowAddForm(false);\n    }\n  };\n\n  const toggleWatchWallet = (address: string) => {\n    const newWatched = new Set(watchedWallets);\n    if (newWatched.has(address)) {\n      newWatched.delete(address);\n    } else {\n      newWatched.add(address);\n    }\n    setWatchedWallets(newWatched);\n  };\n\n  const formatCurrency = (value: number) => {\n    if (Math.abs(value) >= 1000000) {\n      return `$${(value / 1000000).toFixed(2)}M`;\n    } else if (Math.abs(value) >= 1000) {\n      return `$${(value / 1000).toFixed(1)}K`;\n    } else {\n      return `$${value.toFixed(2)}`;\n    }\n  };\n\n  const formatAddress = (address: string) => {\n    return `${address.slice(0, 6)}...${address.slice(-4)}`;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            Wallet Tracker\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Monitor top traders and scalpers on Hyperliquid\n          </p>\n        </div>\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Wallet\n        </button>\n      </div>\n\n      {/* Add Wallet Form */}\n      {showAddForm && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border\">\n          <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n            Add Wallet to Track\n          </h3>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Wallet Address\n              </label>\n              <input\n                type=\"text\"\n                placeholder=\"0x...\"\n                value={newWalletAddress}\n                onChange={(e) => setNewWalletAddress(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Nickname (Optional)\n              </label>\n              <input\n                type=\"text\"\n                placeholder=\"e.g., Whale #1\"\n                value={newWalletNickname}\n                onChange={(e) => setNewWalletNickname(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex justify-end space-x-3 mt-6\">\n            <button\n              onClick={() => setShowAddForm(false)}\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleAddWallet}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Add Wallet\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center mb-4\">\n          <Filter className=\"h-5 w-5 text-gray-500 mr-2\" />\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Filters</h3>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {/* Sort By */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Sort By\n            </label>\n            <select\n              value={filter.sortBy}\n              onChange={(e) => setFilter(prev => ({\n                ...prev,\n                sortBy: e.target.value as WalletFilter['sortBy']\n              }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"pnl24h\">24h PnL</option>\n              <option value=\"pnlAllTime\">All Time PnL</option>\n              <option value=\"volume24h\">24h Volume</option>\n              <option value=\"winRate\">Win Rate</option>\n              <option value=\"tradeCount\">Trade Count</option>\n              <option value=\"scalpingScore\">Scalping Score</option>\n            </select>\n          </div>\n\n          {/* Limit */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Show Top\n            </label>\n            <select\n              value={filter.limit}\n              onChange={(e) => setFilter(prev => ({\n                ...prev,\n                limit: parseInt(e.target.value)\n              }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value={5}>Top 5</option>\n              <option value={10}>Top 10</option>\n              <option value={25}>Top 25</option>\n              <option value={50}>Top 50</option>\n              <option value={100}>Top 100</option>\n            </select>\n          </div>\n\n          {/* Max Trade Size */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Max Trade Size\n            </label>\n            <select\n              value={filter.maxTradeSize || ''}\n              onChange={(e) => setFilter(prev => ({\n                ...prev,\n                maxTradeSize: e.target.value ? parseInt(e.target.value) : undefined\n              }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"\">No Limit</option>\n              <option value={1000}>$1K</option>\n              <option value={5000}>$5K</option>\n              <option value={10000}>$10K</option>\n              <option value={50000}>$50K</option>\n              <option value={100000}>$100K</option>\n            </select>\n          </div>\n\n          {/* Scalpers Only */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Trader Type\n            </label>\n            <select\n              value={filter.onlyScalpers ? 'scalpers' : 'all'}\n              onChange={(e) => setFilter(prev => ({\n                ...prev,\n                onlyScalpers: e.target.value === 'scalpers'\n              }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"all\">All Traders</option>\n              <option value=\"scalpers\">Scalpers Only</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Wallet List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Top Wallets ({filteredWallets.length})\n          </h3>\n        </div>\n\n        <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n          {filteredWallets.length === 0 ? (\n            <div className=\"px-6 py-8 text-center\">\n              <Wallet className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                No wallets found matching your filters.\n              </p>\n            </div>\n          ) : (\n            filteredWallets.map((wallet, index) => (\n              <div\n                key={wallet.address}\n                className=\"px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors\"\n                onClick={() => onWalletClick(wallet)}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n                        #{index + 1}\n                      </span>\n                      {wallet.isScalper && (\n                        <Zap className=\"h-4 w-4 text-yellow-500\" title=\"Scalper\" />\n                      )}\n                    </div>\n\n                    <div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"font-mono text-sm text-gray-900 dark:text-white\">\n                          {formatAddress(wallet.address)}\n                        </span>\n                        {wallet.nickname && (\n                          <span className=\"text-sm text-blue-600 dark:text-blue-400\">\n                            ({wallet.nickname})\n                          </span>\n                        )}\n                      </div>\n\n                      <div className=\"flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400\">\n                        <span className=\"flex items-center\">\n                          <DollarSign className=\"h-3 w-3 mr-1\" />\n                          Vol: {formatCurrency(wallet.totalVolume24h)}\n                        </span>\n                        <span className=\"flex items-center\">\n                          <Target className=\"h-3 w-3 mr-1\" />\n                          Win: {wallet.winRate.toFixed(1)}%\n                        </span>\n                        <span className=\"flex items-center\">\n                          <Clock className=\"h-3 w-3 mr-1\" />\n                          {wallet.tradeCount24h} trades\n                        </span>\n                        {wallet.isScalper && (\n                          <span className=\"flex items-center text-yellow-600 dark:text-yellow-400\">\n                            <Zap className=\"h-3 w-3 mr-1\" />\n                            Score: {wallet.scalpingScore}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"text-right\">\n                      <div className={`text-lg font-semibold ${\n                        wallet.totalPnl24h >= 0\n                          ? 'text-green-600 dark:text-green-400'\n                          : 'text-red-600 dark:text-red-400'\n                      }`}>\n                        {wallet.totalPnl24h >= 0 ? '+' : ''}{formatCurrency(wallet.totalPnl24h)}\n                      </div>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        24h PnL\n                      </div>\n                    </div>\n\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => toggleWatchWallet(wallet.address)}\n                        className={`p-2 rounded-lg transition-colors ${\n                          watchedWallets.has(wallet.address)\n                            ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'\n                            : 'text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'\n                        }`}\n                        title={watchedWallets.has(wallet.address) ? 'Stop watching' : 'Watch wallet'}\n                      >\n                        {watchedWallets.has(wallet.address) ? (\n                          <Eye className=\"h-4 w-4\" />\n                        ) : (\n                          <EyeOff className=\"h-4 w-4\" />\n                        )}\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AA2BA,MAAM,iBAA+B;IACnC,QAAQ;IACR,WAAW;IACX,OAAO;AACT;AAEO,SAAS,cAAc,EAC5B,OAAO,EACP,YAAY,EACZ,WAAW,EACX,cAAc,EACd,aAAa,EACM;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEtE,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,WAAW,QAAQ,MAAM,CAAC,CAAA;YAC5B,IAAI,OAAO,YAAY,IAAI,OAAO,cAAc,GAAG,OAAO,YAAY,EAAE,OAAO;YAC/E,IAAI,OAAO,YAAY,IAAI,OAAO,cAAc,GAAG,OAAO,YAAY,EAAE,OAAO;YAC/E,IAAI,OAAO,SAAS,IAAI,OAAO,WAAW,GAAG,OAAO,SAAS,EAAE,OAAO;YACtE,IAAI,OAAO,SAAS,IAAI,OAAO,WAAW,GAAG,OAAO,SAAS,EAAE,OAAO;YACtE,IAAI,OAAO,YAAY,IAAI,OAAO,YAAY,GAAG,OAAO,YAAY,EAAE,OAAO;YAC7E,IAAI,OAAO,YAAY,IAAI,OAAO,YAAY,GAAG,OAAO,YAAY,EAAE,OAAO;YAC7E,IAAI,OAAO,UAAU,IAAI,OAAO,OAAO,GAAG,OAAO,UAAU,EAAE,OAAO;YACpE,IAAI,OAAO,UAAU,IAAI,OAAO,OAAO,GAAG,OAAO,UAAU,EAAE,OAAO;YACpE,IAAI,OAAO,YAAY,IAAI,CAAC,OAAO,SAAS,EAAE,OAAO;YACrD,IAAI,OAAO,gBAAgB,IAAI,OAAO,aAAa,GAAG,OAAO,gBAAgB,EAAE,OAAO;YACtF,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;gBACzC,MAAM,SAAS,OAAO,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,OAAO,IAAI,CAAC,QAAQ,CAAC;gBAC5D,IAAI,CAAC,QAAQ,OAAO;YACtB;YACA,OAAO;QACT;QAEA,OAAO;QACP,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,IAAI,QAAgB;YACpB,OAAQ,OAAO,MAAM;gBACnB,KAAK;oBACH,SAAS,EAAE,cAAc;oBACzB,SAAS,EAAE,cAAc;oBACzB;gBACF,KAAK;oBACH,SAAS,EAAE,WAAW;oBACtB,SAAS,EAAE,WAAW;oBACtB;gBACF,KAAK;oBACH,SAAS,EAAE,eAAe;oBAC1B,SAAS,EAAE,eAAe;oBAC1B;gBACF,KAAK;oBACH,SAAS,EAAE,OAAO;oBAClB,SAAS,EAAE,OAAO;oBAClB;gBACF,KAAK;oBACH,SAAS,EAAE,aAAa;oBACxB,SAAS,EAAE,aAAa;oBACxB;gBACF,KAAK;oBACH,SAAS,EAAE,aAAa;oBACxB,SAAS,EAAE,aAAa;oBACxB;gBACF;oBACE,SAAS,EAAE,WAAW;oBACtB,SAAS,EAAE,WAAW;YAC1B;YAEA,OAAO,OAAO,SAAS,KAAK,SAAS,SAAS,SAAS,SAAS;QAClE;QAEA,OAAO,SAAS,KAAK,CAAC,GAAG,OAAO,KAAK;IACvC,GAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,kBAAkB;QACtB,IAAI,iBAAiB,IAAI,IAAI;YAC3B,YAAY,iBAAiB,IAAI,IAAI,kBAAkB,IAAI,MAAM;YACjE,oBAAoB;YACpB,qBAAqB;YACrB,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,IAAI,IAAI;QAC3B,IAAI,WAAW,GAAG,CAAC,UAAU;YAC3B,WAAW,MAAM,CAAC;QACpB,OAAO;YACL,WAAW,GAAG,CAAC;QACjB;QACA,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,GAAG,CAAC,UAAU,SAAS;YAC9B,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO,IAAI,KAAK,GAAG,CAAC,UAAU,MAAM;YAClC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO;YACL,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;QAC/B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,IAAI;IACxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,8OAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;;;;;;;;;;;0CAId,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wCACpD,WAAU;;;;;;;;;;;;;;;;;;kCAKhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;;;;;;;kCAGtE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,OAAO,MAAM;wCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAClC,GAAG,IAAI;oDACP,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACxB,CAAC;wCACD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,8OAAC;gDAAO,OAAM;0DAAgB;;;;;;;;;;;;;;;;;;0CAKlC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,OAAO,KAAK;wCACnB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAClC,GAAG,IAAI;oDACP,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;gDAChC,CAAC;wCACD,WAAU;;0DAEV,8OAAC;gDAAO,OAAO;0DAAG;;;;;;0DAClB,8OAAC;gDAAO,OAAO;0DAAI;;;;;;0DACnB,8OAAC;gDAAO,OAAO;0DAAI;;;;;;0DACnB,8OAAC;gDAAO,OAAO;0DAAI;;;;;;0DACnB,8OAAC;gDAAO,OAAO;0DAAK;;;;;;;;;;;;;;;;;;0CAKxB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,OAAO,YAAY,IAAI;wCAC9B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAClC,GAAG,IAAI;oDACP,cAAc,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC5D,CAAC;wCACD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAO;0DAAM;;;;;;0DACrB,8OAAC;gDAAO,OAAO;0DAAM;;;;;;0DACrB,8OAAC;gDAAO,OAAO;0DAAO;;;;;;0DACtB,8OAAC;gDAAO,OAAO;0DAAO;;;;;;0DACtB,8OAAC;gDAAO,OAAO;0DAAQ;;;;;;;;;;;;;;;;;;0CAK3B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,OAAO,YAAY,GAAG,aAAa;wCAC1C,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAClC,GAAG,IAAI;oDACP,cAAc,EAAE,MAAM,CAAC,KAAK,KAAK;gDACnC,CAAC;wCACD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAAsD;gCACpD,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;kCAIzC,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;mCAKlD,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE7B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEAAuD;gEACnE,QAAQ;;;;;;;wDAEX,OAAO,SAAS,kBACf,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;4DAA0B,OAAM;;;;;;;;;;;;8DAInD,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,cAAc,OAAO,OAAO;;;;;;gEAE9B,OAAO,QAAQ,kBACd,8OAAC;oEAAK,WAAU;;wEAA2C;wEACvD,OAAO,QAAQ;wEAAC;;;;;;;;;;;;;sEAKxB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAiB;wEACjC,eAAe,OAAO,cAAc;;;;;;;8EAE5C,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;wEAC7B,OAAO,OAAO,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAElC,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,OAAO,aAAa;wEAAC;;;;;;;gEAEvB,OAAO,SAAS,kBACf,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;wEACxB,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;sDAOtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,sBAAsB,EACrC,OAAO,WAAW,IAAI,IAClB,uCACA,kCACJ;;gEACC,OAAO,WAAW,IAAI,IAAI,MAAM;gEAAI,eAAe,OAAO,WAAW;;;;;;;sEAExE,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;8DAK5D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,SAAS,IAAM,kBAAkB,OAAO,OAAO;wDAC/C,WAAW,CAAC,iCAAiC,EAC3C,eAAe,GAAG,CAAC,OAAO,OAAO,IAC7B,qEACA,0DACJ;wDACF,OAAO,eAAe,GAAG,CAAC,OAAO,OAAO,IAAI,kBAAkB;kEAE7D,eAAe,GAAG,CAAC,OAAO,OAAO,kBAChC,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;iFAEf,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA7EvB,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;AA0FnC"}}, {"offset": {"line": 2345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/WalletAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  Legend\n} from 'recharts';\nimport { WalletData, WalletTrade, ScalpingMetrics } from '@/types/hyperliquid';\nimport {\n  ArrowLeft,\n  TrendingUp,\n  TrendingDown,\n  DollarSign,\n  Target,\n  Zap,\n  Activity,\n  BarChart3,\n  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,\n  Clock,\n  Brain,\n  AlertTriangle,\n  CheckCircle\n} from 'lucide-react';\n\ninterface WalletAnalysisProps {\n  wallet: WalletData;\n  trades: WalletTrade[];\n  scalpingMetrics: ScalpingMetrics;\n  onBack: () => void;\n}\n\nexport function WalletAnalysis({ wallet, trades, scalpingMetrics, onBack }: WalletAnalysisProps) {\n  const [activeChart, setActiveChart] = useState<'pnl' | 'volume' | 'trades' | 'symbols'>('pnl');\n\n  // Process data for charts\n  const chartData = useMemo(() => {\n    const sortedTrades = [...trades].sort((a, b) => a.timestamp - b.timestamp);\n    let cumulativePnl = 0;\n    let cumulativeVolume = 0;\n\n    const timeSeriesData = sortedTrades.map((trade, index) => {\n      cumulativePnl += trade.pnl || 0;\n      cumulativeVolume += trade.value;\n\n      return {\n        timestamp: trade.timestamp,\n        time: new Date(trade.timestamp).toLocaleTimeString(),\n        date: new Date(trade.timestamp).toLocaleDateString(),\n        cumulativePnl,\n        cumulativeVolume,\n        tradePnl: trade.pnl || 0,\n        tradeValue: trade.value,\n        tradeCount: index + 1,\n        symbol: trade.symbol,\n        side: trade.side\n      };\n    });\n\n    return timeSeriesData;\n  }, [trades]);\n\n  const symbolAnalysis = useMemo(() => {\n    const symbolMap = trades.reduce((acc, trade) => {\n      if (!acc[trade.symbol]) {\n        acc[trade.symbol] = {\n          symbol: trade.symbol,\n          totalTrades: 0,\n          totalVolume: 0,\n          totalPnl: 0,\n          winningTrades: 0,\n          losingTrades: 0,\n          avgTradeSize: 0,\n          winRate: 0,\n          profitFactor: 0\n        };\n      }\n\n      const symbolData = acc[trade.symbol];\n      symbolData.totalTrades++;\n      symbolData.totalVolume += trade.value;\n      symbolData.totalPnl += trade.pnl || 0;\n\n      if ((trade.pnl || 0) > 0) {\n        symbolData.winningTrades++;\n      } else if ((trade.pnl || 0) < 0) {\n        symbolData.losingTrades++;\n      }\n\n      return acc;\n    }, {} as Record<string, any>);\n\n    return Object.values(symbolMap).map((item: any) => ({\n      ...item,\n      avgTradeSize: item.totalVolume / item.totalTrades,\n      winRate: (item.winningTrades / item.totalTrades) * 100,\n      profitFactor: item.totalPnl > 0 ?\n        Math.abs(item.totalPnl) / Math.max(Math.abs(item.totalPnl - item.totalPnl), 1) : 0\n    })).sort((a: any, b: any) => b.totalVolume - a.totalVolume);\n  }, [trades]);\n\n  const tradingPatterns = useMemo(() => {\n    const hourlyActivity = new Array(24).fill(0);\n    const dailyActivity = new Array(7).fill(0);\n\n    trades.forEach(trade => {\n      const date = new Date(trade.timestamp);\n      const hour = date.getHours();\n      const day = date.getDay();\n\n      hourlyActivity[hour]++;\n      dailyActivity[day]++;\n    });\n\n    return {\n      hourlyActivity: hourlyActivity.map((count, hour) => ({\n        hour: `${hour}:00`,\n        trades: count,\n        percentage: (count / trades.length) * 100\n      })),\n      dailyActivity: dailyActivity.map((count, day) => ({\n        day: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][day],\n        trades: count,\n        percentage: (count / trades.length) * 100\n      }))\n    };\n  }, [trades]);\n\n  const strategyAnalysis = useMemo(() => {\n    const analysis = {\n      tradingStyle: 'Unknown',\n      riskProfile: 'Medium',\n      preferredSymbols: symbolAnalysis.slice(0, 3).map(s => s.symbol),\n      avgHoldTime: 0,\n      consistency: 0,\n      momentum: 'Neutral',\n      insights: [] as string[]\n    };\n\n    // Determine trading style\n    if (wallet.isScalper && scalpingMetrics.avgTimeBetweenTrades < 10) {\n      analysis.tradingStyle = 'High-Frequency Scalper';\n      analysis.insights.push('Executes rapid trades with minimal hold times');\n    } else if (wallet.avgTradeSize > 50000) {\n      analysis.tradingStyle = 'Whale Trader';\n      analysis.insights.push('Makes large position trades, likely institutional');\n    } else if (wallet.winRate > 70) {\n      analysis.tradingStyle = 'Precision Trader';\n      analysis.insights.push('High win rate suggests careful entry/exit timing');\n    } else {\n      analysis.tradingStyle = 'Active Trader';\n    }\n\n    // Risk profile\n    const maxTradeRatio = wallet.maxTradeSize / wallet.avgTradeSize;\n    if (maxTradeRatio > 5) {\n      analysis.riskProfile = 'High Risk';\n      analysis.insights.push('Significant variation in trade sizes indicates high risk tolerance');\n    } else if (wallet.winRate > 65) {\n      analysis.riskProfile = 'Conservative';\n      analysis.insights.push('Consistent win rate suggests risk-averse approach');\n    }\n\n    // Momentum\n    const recentTrades = trades.slice(-10);\n    const recentPnl = recentTrades.reduce((sum, trade) => sum + (trade.pnl || 0), 0);\n    if (recentPnl > 0) {\n      analysis.momentum = 'Bullish';\n      analysis.insights.push('Recent trades show positive momentum');\n    } else if (recentPnl < 0) {\n      analysis.momentum = 'Bearish';\n      analysis.insights.push('Recent performance shows negative trend');\n    }\n\n    // Consistency\n    const dailyPnls = chartData.reduce((acc, point, index) => {\n      const date = point.date;\n      if (!acc[date]) acc[date] = 0;\n      acc[date] += point.tradePnl;\n      return acc;\n    }, {} as Record<string, number>);\n\n    const pnlValues = Object.values(dailyPnls);\n    const positiveDays = pnlValues.filter(pnl => pnl > 0).length;\n    analysis.consistency = (positiveDays / pnlValues.length) * 100;\n\n    if (analysis.consistency > 70) {\n      analysis.insights.push('Highly consistent daily performance');\n    } else if (analysis.consistency < 40) {\n      analysis.insights.push('Volatile daily performance with mixed results');\n    }\n\n    return analysis;\n  }, [wallet, trades, scalpingMetrics, symbolAnalysis, chartData]);\n\n  const formatCurrency = (value: number) => {\n    if (Math.abs(value) >= 1000000) {\n      return `$${(value / 1000000).toFixed(2)}M`;\n    } else if (Math.abs(value) >= 1000) {\n      return `$${(value / 1000).toFixed(1)}K`;\n    } else {\n      return `$${value.toFixed(2)}`;\n    }\n  };\n\n  const formatAddress = (address: string) => {\n    return `${address.slice(0, 8)}...${address.slice(-6)}`;\n  };\n\n  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <button\n            onClick={onBack}\n            className=\"flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300\"\n          >\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back to Wallet List\n          </button>\n\n          <div className=\"flex items-center space-x-2\">\n            {wallet.isScalper && (\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\">\n                <Zap className=\"h-4 w-4 mr-1\" />\n                Scalper\n              </span>\n            )}\n            {wallet.tags.map(tag => (\n              <span key={tag} className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                {tag}\n              </span>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              {wallet.nickname || 'Wallet Analysis'}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 font-mono text-lg\">\n              {formatAddress(wallet.address)}\n            </p>\n          </div>\n\n          <div className=\"text-right\">\n            <div className={`text-3xl font-bold ${\n              wallet.totalPnl24h >= 0\n                ? 'text-green-600 dark:text-green-400'\n                : 'text-red-600 dark:text-red-400'\n            }`}>\n              {wallet.totalPnl24h >= 0 ? '+' : ''}{formatCurrency(wallet.totalPnl24h)}\n            </div>\n            <div className=\"text-gray-500 dark:text-gray-400\">24h PnL</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Key Metrics Grid */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <DollarSign className=\"h-6 w-6 text-blue-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {formatCurrency(wallet.totalVolume24h)}\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">24h Volume</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <Target className=\"h-6 w-6 text-green-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {wallet.winRate.toFixed(1)}%\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Win Rate</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <Activity className=\"h-6 w-6 text-purple-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {wallet.tradeCount24h}\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Trades</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <BarChart3 className=\"h-6 w-6 text-orange-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {formatCurrency(wallet.avgTradeSize)}\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Avg Trade</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <Clock className=\"h-6 w-6 text-indigo-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {scalpingMetrics.avgTimeBetweenTrades.toFixed(1)}m\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Avg Time</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <Brain className=\"h-6 w-6 text-pink-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {strategyAnalysis.consistency.toFixed(0)}%\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Consistency</div>\n        </div>\n      </div>\n\n      {/* Strategy Analysis */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center\">\n          <Brain className=\"h-5 w-5 mr-2 text-pink-500\" />\n          Strategy Analysis\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Trading Style</h4>\n            <p className=\"text-blue-600 dark:text-blue-400 font-medium\">{strategyAnalysis.tradingStyle}</p>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n              Risk Profile: {strategyAnalysis.riskProfile}\n            </p>\n          </div>\n\n          <div>\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Preferred Assets</h4>\n            <div className=\"flex flex-wrap gap-1\">\n              {strategyAnalysis.preferredSymbols.map(symbol => (\n                <span key={symbol} className=\"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-sm\">\n                  {symbol}\n                </span>\n              ))}\n            </div>\n          </div>\n\n          <div>\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Current Momentum</h4>\n            <div className={`flex items-center ${\n              strategyAnalysis.momentum === 'Bullish' ? 'text-green-600 dark:text-green-400' :\n              strategyAnalysis.momentum === 'Bearish' ? 'text-red-600 dark:text-red-400' :\n              'text-gray-600 dark:text-gray-400'\n            }`}>\n              {strategyAnalysis.momentum === 'Bullish' ? <TrendingUp className=\"h-4 w-4 mr-1\" /> :\n               strategyAnalysis.momentum === 'Bearish' ? <TrendingDown className=\"h-4 w-4 mr-1\" /> :\n               <Activity className=\"h-4 w-4 mr-1\" />}\n              {strategyAnalysis.momentum}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-4\">\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Key Insights</h4>\n          <ul className=\"space-y-1\">\n            {strategyAnalysis.insights.map((insight, index) => (\n              <li key={index} className=\"flex items-start text-sm text-gray-600 dark:text-gray-400\">\n                <CheckCircle className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                {insight}\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n\n      {/* Chart Navigation */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Performance Charts\n          </h3>\n\n          <div className=\"flex space-x-2\">\n            {[\n              { id: 'pnl', label: 'PnL Curve', icon: TrendingUp },\n              { id: 'volume', label: 'Volume', icon: BarChart3 },\n              { id: 'trades', label: 'Trade Activity', icon: Activity },\n              { id: 'symbols', label: 'Asset Distribution', icon: PieChartIcon }\n            ].map(({ id, label, icon: Icon }) => (\n              <button\n                key={id}\n                onClick={() => setActiveChart(id as any)}\n                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${\n                  activeChart === id\n                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'\n                    : 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200'\n                }`}\n              >\n                <Icon className=\"h-4 w-4 mr-2\" />\n                {label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"h-80\">\n          {activeChart === 'pnl' && (\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <AreaChart data={chartData}>\n                <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n                <XAxis dataKey=\"time\" tick={{ fontSize: 12 }} />\n                <YAxis tick={{ fontSize: 12 }} />\n                <Tooltip\n                  formatter={(value: number) => [formatCurrency(value), 'Cumulative PnL']}\n                  labelFormatter={(label) => `Time: ${label}`}\n                />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"cumulativePnl\"\n                  stroke=\"#3B82F6\"\n                  fill=\"#3B82F6\"\n                  fillOpacity={0.1}\n                />\n              </AreaChart>\n            </ResponsiveContainer>\n          )}\n\n          {activeChart === 'volume' && (\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <BarChart data={chartData.slice(-20)}>\n                <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n                <XAxis dataKey=\"time\" tick={{ fontSize: 12 }} />\n                <YAxis tick={{ fontSize: 12 }} />\n                <Tooltip\n                  formatter={(value: number) => [formatCurrency(value), 'Trade Value']}\n                />\n                <Bar dataKey=\"tradeValue\" fill=\"#10B981\" />\n              </BarChart>\n            </ResponsiveContainer>\n          )}\n\n          {activeChart === 'trades' && (\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <LineChart data={tradingPatterns.hourlyActivity}>\n                <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n                <XAxis dataKey=\"hour\" tick={{ fontSize: 12 }} />\n                <YAxis tick={{ fontSize: 12 }} />\n                <Tooltip />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"trades\"\n                  stroke=\"#F59E0B\"\n                  strokeWidth={2}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          )}\n\n          {activeChart === 'symbols' && (\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <PieChart>\n                <Pie\n                  data={symbolAnalysis}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  outerRadius={100}\n                  dataKey=\"totalVolume\"\n                  nameKey=\"symbol\"\n                  label={({ symbol, percent }) => `${symbol} ${(percent * 100).toFixed(0)}%`}\n                >\n                  {symbolAnalysis.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                  ))}\n                </Pie>\n                <Tooltip formatter={(value: number) => [formatCurrency(value), 'Volume']} />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          )}\n        </div>\n      </div>\n\n      {/* Detailed Trade History */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center\">\n          <Activity className=\"h-5 w-5 mr-2\" />\n          Complete Trade History ({trades.length} trades)\n        </h3>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full text-sm\">\n            <thead>\n              <tr className=\"border-b border-gray-200 dark:border-gray-700\">\n                <th className=\"text-left py-3 text-gray-500 dark:text-gray-400\">Time</th>\n                <th className=\"text-left py-3 text-gray-500 dark:text-gray-400\">Symbol</th>\n                <th className=\"text-left py-3 text-gray-500 dark:text-gray-400\">Side</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">Size</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">Price</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">Value</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">PnL</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">Fee</th>\n              </tr>\n            </thead>\n            <tbody>\n              {trades\n                .sort((a, b) => b.timestamp - a.timestamp)\n                .slice(0, 50)\n                .map((trade) => (\n                <tr key={trade.tradeId} className=\"border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"py-3 text-gray-600 dark:text-gray-400\">\n                    {new Date(trade.timestamp).toLocaleString()}\n                  </td>\n                  <td className=\"py-3 font-medium text-gray-900 dark:text-white\">\n                    {trade.symbol}\n                  </td>\n                  <td className=\"py-3\">\n                    <span className={`px-2 py-1 rounded text-xs font-medium ${\n                      trade.side === 'buy'\n                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n                    }`}>\n                      {trade.side.toUpperCase()}\n                    </span>\n                  </td>\n                  <td className=\"py-3 text-right font-mono text-gray-900 dark:text-white\">\n                    {trade.size.toFixed(4)}\n                  </td>\n                  <td className=\"py-3 text-right font-mono text-gray-900 dark:text-white\">\n                    ${trade.price.toFixed(2)}\n                  </td>\n                  <td className=\"py-3 text-right font-mono text-gray-900 dark:text-white\">\n                    {formatCurrency(trade.value)}\n                  </td>\n                  <td className={`py-3 text-right font-mono ${\n                    (trade.pnl || 0) >= 0\n                      ? 'text-green-600 dark:text-green-400'\n                      : 'text-red-600 dark:text-red-400'\n                  }`}>\n                    {(trade.pnl || 0) >= 0 ? '+' : ''}{formatCurrency(trade.pnl || 0)}\n                  </td>\n                  <td className=\"py-3 text-right font-mono text-gray-500 dark:text-gray-400\">\n                    {formatCurrency(trade.fee)}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {trades.length > 50 && (\n          <div className=\"mt-4 text-center\">\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              Showing latest 50 trades of {trades.length} total\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Symbol Performance Breakdown */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center\">\n          <BarChart3 className=\"h-5 w-5 mr-2\" />\n          Performance by Symbol\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {symbolAnalysis.map((symbol: any) => (\n            <div key={symbol.symbol} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">{symbol.symbol}</h4>\n                <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {symbol.totalTrades} trades\n                </span>\n              </div>\n\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Total Volume:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {formatCurrency(symbol.totalVolume)}\n                  </span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Total PnL:</span>\n                  <span className={`text-sm font-medium ${\n                    symbol.totalPnl >= 0\n                      ? 'text-green-600 dark:text-green-400'\n                      : 'text-red-600 dark:text-red-400'\n                  }`}>\n                    {symbol.totalPnl >= 0 ? '+' : ''}{formatCurrency(symbol.totalPnl)}\n                  </span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Win Rate:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {symbol.winRate.toFixed(1)}%\n                  </span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Avg Trade:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {formatCurrency(symbol.avgTradeSize)}\n                  </span>\n                </div>\n\n                {/* Win Rate Progress Bar */}\n                <div className=\"mt-3\">\n                  <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    <span>Win Rate</span>\n                    <span>{symbol.winRate.toFixed(1)}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div\n                      className={`h-2 rounded-full ${\n                        symbol.winRate >= 60 ? 'bg-green-500' :\n                        symbol.winRate >= 40 ? 'bg-yellow-500' : 'bg-red-500'\n                      }`}\n                      style={{ width: `${Math.min(symbol.winRate, 100)}%` }}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAmBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAlBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;AA4CO,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAuB;IAC7F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2C;IAExF,0BAA0B;IAC1B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,eAAe;eAAI;SAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;QACzE,IAAI,gBAAgB;QACpB,IAAI,mBAAmB;QAEvB,MAAM,iBAAiB,aAAa,GAAG,CAAC,CAAC,OAAO;YAC9C,iBAAiB,MAAM,GAAG,IAAI;YAC9B,oBAAoB,MAAM,KAAK;YAE/B,OAAO;gBACL,WAAW,MAAM,SAAS;gBAC1B,MAAM,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;gBAClD,MAAM,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;gBAClD;gBACA;gBACA,UAAU,MAAM,GAAG,IAAI;gBACvB,YAAY,MAAM,KAAK;gBACvB,YAAY,QAAQ;gBACpB,QAAQ,MAAM,MAAM;gBACpB,MAAM,MAAM,IAAI;YAClB;QACF;QAEA,OAAO;IACT,GAAG;QAAC;KAAO;IAEX,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,YAAY,OAAO,MAAM,CAAC,CAAC,KAAK;YACpC,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE;gBACtB,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG;oBAClB,QAAQ,MAAM,MAAM;oBACpB,aAAa;oBACb,aAAa;oBACb,UAAU;oBACV,eAAe;oBACf,cAAc;oBACd,cAAc;oBACd,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,MAAM,aAAa,GAAG,CAAC,MAAM,MAAM,CAAC;YACpC,WAAW,WAAW;YACtB,WAAW,WAAW,IAAI,MAAM,KAAK;YACrC,WAAW,QAAQ,IAAI,MAAM,GAAG,IAAI;YAEpC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG;gBACxB,WAAW,aAAa;YAC1B,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG;gBAC/B,WAAW,YAAY;YACzB;YAEA,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO,OAAO,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,OAAc,CAAC;gBAClD,GAAG,IAAI;gBACP,cAAc,KAAK,WAAW,GAAG,KAAK,WAAW;gBACjD,SAAS,AAAC,KAAK,aAAa,GAAG,KAAK,WAAW,GAAI;gBACnD,cAAc,KAAK,QAAQ,GAAG,IAC5B,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK;YACrF,CAAC,GAAG,IAAI,CAAC,CAAC,GAAQ,IAAW,EAAE,WAAW,GAAG,EAAE,WAAW;IAC5D,GAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,MAAM,iBAAiB,IAAI,MAAM,IAAI,IAAI,CAAC;QAC1C,MAAM,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;QAExC,OAAO,OAAO,CAAC,CAAA;YACb,MAAM,OAAO,IAAI,KAAK,MAAM,SAAS;YACrC,MAAM,OAAO,KAAK,QAAQ;YAC1B,MAAM,MAAM,KAAK,MAAM;YAEvB,cAAc,CAAC,KAAK;YACpB,aAAa,CAAC,IAAI;QACpB;QAEA,OAAO;YACL,gBAAgB,eAAe,GAAG,CAAC,CAAC,OAAO,OAAS,CAAC;oBACnD,MAAM,GAAG,KAAK,GAAG,CAAC;oBAClB,QAAQ;oBACR,YAAY,AAAC,QAAQ,OAAO,MAAM,GAAI;gBACxC,CAAC;YACD,eAAe,cAAc,GAAG,CAAC,CAAC,OAAO,MAAQ,CAAC;oBAChD,KAAK;wBAAC;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;qBAAM,CAAC,IAAI;oBAC3D,QAAQ;oBACR,YAAY,AAAC,QAAQ,OAAO,MAAM,GAAI;gBACxC,CAAC;QACH;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,MAAM,WAAW;YACf,cAAc;YACd,aAAa;YACb,kBAAkB,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAC9D,aAAa;YACb,aAAa;YACb,UAAU;YACV,UAAU,EAAE;QACd;QAEA,0BAA0B;QAC1B,IAAI,OAAO,SAAS,IAAI,gBAAgB,oBAAoB,GAAG,IAAI;YACjE,SAAS,YAAY,GAAG;YACxB,SAAS,QAAQ,CAAC,IAAI,CAAC;QACzB,OAAO,IAAI,OAAO,YAAY,GAAG,OAAO;YACtC,SAAS,YAAY,GAAG;YACxB,SAAS,QAAQ,CAAC,IAAI,CAAC;QACzB,OAAO,IAAI,OAAO,OAAO,GAAG,IAAI;YAC9B,SAAS,YAAY,GAAG;YACxB,SAAS,QAAQ,CAAC,IAAI,CAAC;QACzB,OAAO;YACL,SAAS,YAAY,GAAG;QAC1B;QAEA,eAAe;QACf,MAAM,gBAAgB,OAAO,YAAY,GAAG,OAAO,YAAY;QAC/D,IAAI,gBAAgB,GAAG;YACrB,SAAS,WAAW,GAAG;YACvB,SAAS,QAAQ,CAAC,IAAI,CAAC;QACzB,OAAO,IAAI,OAAO,OAAO,GAAG,IAAI;YAC9B,SAAS,WAAW,GAAG;YACvB,SAAS,QAAQ,CAAC,IAAI,CAAC;QACzB;QAEA,WAAW;QACX,MAAM,eAAe,OAAO,KAAK,CAAC,CAAC;QACnC,MAAM,YAAY,aAAa,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG;QAC9E,IAAI,YAAY,GAAG;YACjB,SAAS,QAAQ,GAAG;YACpB,SAAS,QAAQ,CAAC,IAAI,CAAC;QACzB,OAAO,IAAI,YAAY,GAAG;YACxB,SAAS,QAAQ,GAAG;YACpB,SAAS,QAAQ,CAAC,IAAI,CAAC;QACzB;QAEA,cAAc;QACd,MAAM,YAAY,UAAU,MAAM,CAAC,CAAC,KAAK,OAAO;YAC9C,MAAM,OAAO,MAAM,IAAI;YACvB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG;YAC5B,GAAG,CAAC,KAAK,IAAI,MAAM,QAAQ;YAC3B,OAAO;QACT,GAAG,CAAC;QAEJ,MAAM,YAAY,OAAO,MAAM,CAAC;QAChC,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,MAAO,MAAM,GAAG,MAAM;QAC5D,SAAS,WAAW,GAAG,AAAC,eAAe,UAAU,MAAM,GAAI;QAE3D,IAAI,SAAS,WAAW,GAAG,IAAI;YAC7B,SAAS,QAAQ,CAAC,IAAI,CAAC;QACzB,OAAO,IAAI,SAAS,WAAW,GAAG,IAAI;YACpC,SAAS,QAAQ,CAAC,IAAI,CAAC;QACzB;QAEA,OAAO;IACT,GAAG;QAAC;QAAQ;QAAQ;QAAiB;QAAgB;KAAU;IAE/D,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,GAAG,CAAC,UAAU,SAAS;YAC9B,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO,IAAI,KAAK,GAAG,CAAC,UAAU,MAAM;YAClC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO;YACL,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;QAC/B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,IAAI;IACxD;IAEA,MAAM,SAAS;QAAC;QAAW;QAAW;QAAW;QAAW;QAAW;KAAU;IAEjF,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIxC,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,SAAS,kBACf,8OAAC;wCAAK,WAAU;;0DACd,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAInC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA,oBACf,8OAAC;4CAAe,WAAU;sDACvB;2CADQ;;;;;;;;;;;;;;;;;kCAOjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,OAAO,QAAQ,IAAI;;;;;;kDAEtB,8OAAC;wCAAE,WAAU;kDACV,cAAc,OAAO,OAAO;;;;;;;;;;;;0CAIjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,mBAAmB,EAClC,OAAO,WAAW,IAAI,IAClB,uCACA,kCACJ;;4CACC,OAAO,WAAW,IAAI,IAAI,MAAM;4CAAI,eAAe,OAAO,WAAW;;;;;;;kDAExE,8OAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;0BAMxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAI,WAAU;0CACZ,eAAe,OAAO,cAAc;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,OAAO,CAAC,OAAO,CAAC;oCAAG;;;;;;;0CAE7B,8OAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAI,WAAU;0CACZ,OAAO,aAAa;;;;;;0CAEvB,8OAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAI,WAAU;0CACZ,eAAe,OAAO,YAAY;;;;;;0CAErC,8OAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAI,WAAU;;oCACZ,gBAAgB,oBAAoB,CAAC,OAAO,CAAC;oCAAG;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAI,WAAU;;oCACZ,iBAAiB,WAAW,CAAC,OAAO,CAAC;oCAAG;;;;;;;0CAE3C,8OAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;0BAK9D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAA+B;;;;;;;kCAIlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;kDAAgD,iBAAiB,YAAY;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;;4CAAgD;4CAC5C,iBAAiB,WAAW;;;;;;;;;;;;;0CAI/C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAC/D,8OAAC;wCAAI,WAAU;kDACZ,iBAAiB,gBAAgB,CAAC,GAAG,CAAC,CAAA,uBACrC,8OAAC;gDAAkB,WAAU;0DAC1B;+CADQ;;;;;;;;;;;;;;;;0CAOjB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAC/D,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EACjC,iBAAiB,QAAQ,KAAK,YAAY,uCAC1C,iBAAiB,QAAQ,KAAK,YAAY,mCAC1C,oCACA;;4CACC,iBAAiB,QAAQ,KAAK,0BAAY,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;uDAChE,iBAAiB,QAAQ,KAAK,0BAAY,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;qEAClE,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACpB,iBAAiB,QAAQ;;;;;;;;;;;;;;;;;;;kCAKhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAC/D,8OAAC;gCAAG,WAAU;0CACX,iBAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACvC,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB;;uCAFM;;;;;;;;;;;;;;;;;;;;;;0BAUjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAIpE,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,IAAI;wCAAO,OAAO;wCAAa,MAAM,kNAAA,CAAA,aAAU;oCAAC;oCAClD;wCAAE,IAAI;wCAAU,OAAO;wCAAU,MAAM,kNAAA,CAAA,YAAS;oCAAC;oCACjD;wCAAE,IAAI;wCAAU,OAAO;wCAAkB,MAAM,0MAAA,CAAA,WAAQ;oCAAC;oCACxD;wCAAE,IAAI;wCAAW,OAAO;wCAAsB,MAAM,8MAAA,CAAA,WAAY;oCAAC;iCAClE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC9B,8OAAC;wCAEC,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,6EAA6E,EACvF,gBAAgB,KACZ,kEACA,iFACJ;;0DAEF,8OAAC;gDAAK,WAAU;;;;;;4CACf;;uCATI;;;;;;;;;;;;;;;;kCAeb,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,uBACf,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,WAAU;;;;;;sDAC/C,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC3C,8OAAC,qJAAA,CAAA,QAAK;4CAAC,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC5B,8OAAC,uJAAA,CAAA,UAAO;4CACN,WAAW,CAAC,QAAkB;oDAAC,eAAe;oDAAQ;iDAAiB;4CACvE,gBAAgB,CAAC,QAAU,CAAC,MAAM,EAAE,OAAO;;;;;;sDAE7C,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,MAAK;4CACL,aAAa;;;;;;;;;;;;;;;;;4BAMpB,gBAAgB,0BACf,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;oCAAC,MAAM,UAAU,KAAK,CAAC,CAAC;;sDAC/B,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,WAAU;;;;;;sDAC/C,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC3C,8OAAC,qJAAA,CAAA,QAAK;4CAAC,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC5B,8OAAC,uJAAA,CAAA,UAAO;4CACN,WAAW,CAAC,QAAkB;oDAAC,eAAe;oDAAQ;iDAAc;;;;;;sDAEtE,8OAAC,mJAAA,CAAA,MAAG;4CAAC,SAAQ;4CAAa,MAAK;;;;;;;;;;;;;;;;;4BAKpC,gBAAgB,0BACf,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM,gBAAgB,cAAc;;sDAC7C,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,WAAU;;;;;;sDAC/C,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC3C,8OAAC,qJAAA,CAAA,QAAK;4CAAC,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC5B,8OAAC,uJAAA,CAAA,UAAO;;;;;sDACR,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,aAAa;;;;;;;;;;;;;;;;;4BAMpB,gBAAgB,2BACf,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;sDACP,8OAAC,+IAAA,CAAA,MAAG;4CACF,MAAM;4CACN,IAAG;4CACH,IAAG;4CACH,aAAa;4CACb,SAAQ;4CACR,SAAQ;4CACR,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAK,GAAG,OAAO,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;sDAEzE,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,oJAAA,CAAA,OAAI;oDAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;mDAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;sDAG9B,8OAAC,uJAAA,CAAA,UAAO;4CAAC,WAAW,CAAC,QAAkB;oDAAC,eAAe;oDAAQ;iDAAS;;;;;;sDACxE,8OAAC,sJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;4BACZ,OAAO,MAAM;4BAAC;;;;;;;kCAGzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;8CACC,cAAA,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;;;;;;;;;;;;8CAGrE,8OAAC;8CACE,OACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,sBACN,8OAAC;4CAAuB,WAAU;;8DAChC,8OAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc;;;;;;8DAE3C,8OAAC;oDAAG,WAAU;8DACX,MAAM,MAAM;;;;;;8DAEf,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,sCAAsC,EACtD,MAAM,IAAI,KAAK,QACX,sEACA,6DACJ;kEACC,MAAM,IAAI,CAAC,WAAW;;;;;;;;;;;8DAG3B,8OAAC;oDAAG,WAAU;8DACX,MAAM,IAAI,CAAC,OAAO,CAAC;;;;;;8DAEtB,8OAAC;oDAAG,WAAU;;wDAA0D;wDACpE,MAAM,KAAK,CAAC,OAAO,CAAC;;;;;;;8DAExB,8OAAC;oDAAG,WAAU;8DACX,eAAe,MAAM,KAAK;;;;;;8DAE7B,8OAAC;oDAAG,WAAW,CAAC,0BAA0B,EACxC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAChB,uCACA,kCACJ;;wDACC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,MAAM;wDAAI,eAAe,MAAM,GAAG,IAAI;;;;;;;8DAEjE,8OAAC;oDAAG,WAAU;8DACX,eAAe,MAAM,GAAG;;;;;;;2CAjCpB,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;oBAyC7B,OAAO,MAAM,GAAG,oBACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAA2C;gCACzB,OAAO,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAOnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,8OAAC;gCAAwB,WAAU;;kDACjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6C,OAAO,MAAM;;;;;;0DACxE,8OAAC;gDAAK,WAAU;;oDACb,OAAO,WAAW;oDAAC;;;;;;;;;;;;;kDAIxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,8OAAC;wDAAK,WAAU;kEACb,eAAe,OAAO,WAAW;;;;;;;;;;;;0DAItC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,8OAAC;wDAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,QAAQ,IAAI,IACf,uCACA,kCACJ;;4DACC,OAAO,QAAQ,IAAI,IAAI,MAAM;4DAAI,eAAe,OAAO,QAAQ;;;;;;;;;;;;;0DAIpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,8OAAC;wDAAK,WAAU;;4DACb,OAAO,OAAO,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,8OAAC;wDAAK,WAAU;kEACb,eAAe,OAAO,YAAY;;;;;;;;;;;;0DAKvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAM,OAAO,OAAO,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEnC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAW,CAAC,iBAAiB,EAC3B,OAAO,OAAO,IAAI,KAAK,iBACvB,OAAO,OAAO,IAAI,KAAK,kBAAkB,cACzC;4DACF,OAAO;gEAAE,OAAO,GAAG,KAAK,GAAG,CAAC,OAAO,OAAO,EAAE,KAAK,CAAC,CAAC;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;+BArDpD,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;AAgEnC"}}, {"offset": {"line": 3898, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3904, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/hooks/useHyperliquidWebSocket.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport {\n  HyperliquidSubscription,\n  PriceData,\n  OrderBookData,\n  TradeData,\n  CandleData,\n  ConnectionStatus,\n  SubscriptionState,\n  HyperliquidWebSocketResponse,\n  OrderBookLevel\n} from '@/types/hyperliquid';\n\nconst HYPERLIQUID_WS_URL = 'wss://api.hyperliquid.xyz/ws';\n\nexport function useHyperliquidWebSocket() {\n  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');\n  const [subscriptions, setSubscriptions] = useState<SubscriptionState[]>([]);\n  const [priceData, setPriceData] = useState<PriceData[]>([\n    // Mock data for demonstration\n    {\n      symbol: 'HYPER',\n      price: 28.50,\n      timestamp: Date.now(),\n      volume: 2456789,\n      change24h: 5.8,\n      high24h: 29.20,\n      low24h: 27.10\n    },\n    {\n      symbol: 'BTC',\n      price: 45000,\n      timestamp: Date.now(),\n      volume: 1234567,\n      change24h: 2.5,\n      high24h: 46000,\n      low24h: 44000\n    },\n    {\n      symbol: 'ETH',\n      price: 3200,\n      timestamp: Date.now(),\n      volume: 987654,\n      change24h: -1.2,\n      high24h: 3300,\n      low24h: 3100\n    }\n  ]);\n  const [orderBookData, setOrderBookData] = useState<OrderBookData | null>({\n    symbol: 'HYPER',\n    bids: [\n      { price: 28.45, size: 150.5 },\n      { price: 28.40, size: 220.2 },\n      { price: 28.35, size: 180.8 },\n      { price: 28.30, size: 310.1 },\n      { price: 28.25, size: 275.5 }\n    ],\n    asks: [\n      { price: 28.55, size: 125.7 },\n      { price: 28.60, size: 190.1 },\n      { price: 28.65, size: 165.9 },\n      { price: 28.70, size: 240.8 },\n      { price: 28.75, size: 200.3 }\n    ],\n    timestamp: Date.now()\n  });\n  const [tradeData, setTradeData] = useState<TradeData[]>([]);\n\n  const wsRef = useRef<WebSocket | null>(null);\n  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const subscriptionIdCounter = useRef(0);\n\n  const connect = useCallback(() => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      return;\n    }\n\n    setConnectionStatus('connecting');\n\n    try {\n      // Check if WebSocket is available in the browser\n      if (typeof WebSocket === 'undefined') {\n        console.error('WebSocket is not supported in this environment');\n        setConnectionStatus('error');\n        return;\n      }\n\n      const ws = new WebSocket(HYPERLIQUID_WS_URL);\n      wsRef.current = ws;\n\n      ws.onopen = () => {\n        console.log('WebSocket connected to Hyperliquid');\n        setConnectionStatus('connected');\n\n        // Resubscribe to active subscriptions\n        subscriptions.forEach(sub => {\n          if (sub.active) {\n            try {\n              ws.send(JSON.stringify({\n                method: 'subscribe',\n                subscription: sub.subscription\n              }));\n            } catch (error) {\n              console.error('Error sending subscription:', error);\n            }\n          }\n        });\n      };\n\n      ws.onmessage = (event) => {\n        try {\n          const message: HyperliquidWebSocketResponse = JSON.parse(event.data);\n          handleWebSocketMessage(message);\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n\n      ws.onclose = (event) => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        setConnectionStatus('disconnected');\n        wsRef.current = null;\n\n        // Auto-reconnect after 3 seconds if not manually disconnected\n        if (event.code !== 1000 && event.code !== 1001) {\n          reconnectTimeoutRef.current = setTimeout(() => {\n            console.log('Attempting to reconnect...');\n            connect();\n          }, 3000);\n        }\n      };\n\n      ws.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setConnectionStatus('error');\n      };\n\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      setConnectionStatus('error');\n    }\n  }, [subscriptions]);\n\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'Manual disconnect');\n      wsRef.current = null;\n    }\n    setConnectionStatus('disconnected');\n  }, []);\n\n  const handleWebSocketMessage = useCallback((message: HyperliquidWebSocketResponse) => {\n    const { channel, data } = message;\n\n    switch (channel) {\n      case 'trades':\n        if (data.trades) {\n          setTradeData(prev => [...prev.slice(-99), ...data.trades!].slice(-100));\n\n          // Update price data from trades\n          data.trades.forEach(trade => {\n            setPriceData(prev => {\n              const existing = prev.find(p => p.symbol === trade.symbol);\n              if (existing) {\n                return prev.map(p =>\n                  p.symbol === trade.symbol\n                    ? { ...p, price: trade.price, timestamp: trade.timestamp }\n                    : p\n                );\n              } else {\n                return [...prev, {\n                  symbol: trade.symbol,\n                  price: trade.price,\n                  timestamp: trade.timestamp\n                }];\n              }\n            });\n          });\n        }\n        break;\n\n      case 'l2Book':\n        if (data.levels && data.coin) {\n          const [bids, asks] = data.levels;\n          setOrderBookData({\n            symbol: data.coin,\n            bids: bids || [],\n            asks: asks || [],\n            timestamp: Date.now()\n          });\n        }\n        break;\n\n      case 'candle':\n        if (data.candle) {\n          setPriceData(prev => {\n            const existing = prev.find(p => p.symbol === data.candle!.symbol);\n            if (existing) {\n              return prev.map(p =>\n                p.symbol === data.candle!.symbol\n                  ? {\n                      ...p,\n                      price: data.candle!.close,\n                      timestamp: data.candle!.timestamp,\n                      volume: data.candle!.volume,\n                      high24h: data.candle!.high,\n                      low24h: data.candle!.low\n                    }\n                  : p\n              );\n            } else {\n              return [...prev, {\n                symbol: data.candle!.symbol,\n                price: data.candle!.close,\n                timestamp: data.candle!.timestamp,\n                volume: data.candle!.volume,\n                high24h: data.candle!.high,\n                low24h: data.candle!.low\n              }];\n            }\n          });\n        }\n        break;\n\n      default:\n        console.log('Unhandled channel:', channel, data);\n    }\n  }, []);\n\n  const subscribe = useCallback((subscription: HyperliquidSubscription) => {\n    const id = `sub_${subscriptionIdCounter.current++}`;\n\n    const newSubscription: SubscriptionState = {\n      id,\n      subscription,\n      active: true,\n      lastUpdate: Date.now()\n    };\n\n    setSubscriptions(prev => [...prev, newSubscription]);\n\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      try {\n        wsRef.current.send(JSON.stringify({\n          method: 'subscribe',\n          subscription\n        }));\n        console.log('Subscribed to:', subscription);\n      } catch (error) {\n        console.error('Error sending subscription:', error);\n      }\n    } else {\n      console.log('WebSocket not connected, subscription queued:', subscription);\n    }\n\n    return id;\n  }, []);\n\n  const unsubscribe = useCallback((subscriptionId: string) => {\n    setSubscriptions(prev => {\n      const subscription = prev.find(sub => sub.id === subscriptionId);\n      if (subscription && wsRef.current?.readyState === WebSocket.OPEN) {\n        wsRef.current.send(JSON.stringify({\n          method: 'unsubscribe',\n          subscription: subscription.subscription\n        }));\n      }\n      return prev.filter(sub => sub.id !== subscriptionId);\n    });\n  }, []);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n\n  return {\n    connectionStatus,\n    subscriptions: subscriptions.map(sub => sub.subscription),\n    priceData,\n    orderBookData,\n    tradeData,\n    connect,\n    disconnect,\n    subscribe,\n    unsubscribe\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAeA,MAAM,qBAAqB;AAEpB,SAAS;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QACtD,8BAA8B;QAC9B;YACE,QAAQ;YACR,OAAO;YACP,WAAW,KAAK,GAAG;YACnB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,QAAQ;QACV;QACA;YACE,QAAQ;YACR,OAAO;YACP,WAAW,KAAK,GAAG;YACnB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,QAAQ;QACV;QACA;YACE,QAAQ;YACR,OAAO;YACP,WAAW,KAAK,GAAG;YACnB,QAAQ;YACR,WAAW,CAAC;YACZ,SAAS;YACT,QAAQ;QACV;KACD;IACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;QACvE,QAAQ;QACR,MAAM;YACJ;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;SAC7B;QACD,MAAM;YACJ;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;SAC7B;QACD,WAAW,KAAK,GAAG;IACrB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAE1D,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IACvC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAC1D,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAErC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;YAChD;QACF;QAEA,oBAAoB;QAEpB,IAAI;YACF,iDAAiD;YACjD,IAAI,OAAO,cAAc,aAAa;gBACpC,QAAQ,KAAK,CAAC;gBACd,oBAAoB;gBACpB;YACF;YAEA,MAAM,KAAK,IAAI,UAAU;YACzB,MAAM,OAAO,GAAG;YAEhB,GAAG,MAAM,GAAG;gBACV,QAAQ,GAAG,CAAC;gBACZ,oBAAoB;gBAEpB,sCAAsC;gBACtC,cAAc,OAAO,CAAC,CAAA;oBACpB,IAAI,IAAI,MAAM,EAAE;wBACd,IAAI;4BACF,GAAG,IAAI,CAAC,KAAK,SAAS,CAAC;gCACrB,QAAQ;gCACR,cAAc,IAAI,YAAY;4BAChC;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,+BAA+B;wBAC/C;oBACF;gBACF;YACF;YAEA,GAAG,SAAS,GAAG,CAAC;gBACd,IAAI;oBACF,MAAM,UAAwC,KAAK,KAAK,CAAC,MAAM,IAAI;oBACnE,uBAAuB;gBACzB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;gBACpD;YACF;YAEA,GAAG,OAAO,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,2BAA2B,MAAM,IAAI,EAAE,MAAM,MAAM;gBAC/D,oBAAoB;gBACpB,MAAM,OAAO,GAAG;gBAEhB,8DAA8D;gBAC9D,IAAI,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,MAAM;oBAC9C,oBAAoB,OAAO,GAAG,WAAW;wBACvC,QAAQ,GAAG,CAAC;wBACZ;oBACF,GAAG;gBACL;YACF;YAEA,GAAG,OAAO,GAAG,CAAC;gBACZ,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,oBAAoB;YACtB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,oBAAoB;QACtB;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,oBAAoB,OAAO,EAAE;YAC/B,aAAa,oBAAoB,OAAO;YACxC,oBAAoB,OAAO,GAAG;QAChC;QAEA,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM;YAC1B,MAAM,OAAO,GAAG;QAClB;QACA,oBAAoB;IACtB,GAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG;QAE1B,OAAQ;YACN,KAAK;gBACH,IAAI,KAAK,MAAM,EAAE;oBACf,aAAa,CAAA,OAAQ;+BAAI,KAAK,KAAK,CAAC,CAAC;+BAAQ,KAAK,MAAM;yBAAE,CAAC,KAAK,CAAC,CAAC;oBAElE,gCAAgC;oBAChC,KAAK,MAAM,CAAC,OAAO,CAAC,CAAA;wBAClB,aAAa,CAAA;4BACX,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,MAAM,MAAM;4BACzD,IAAI,UAAU;gCACZ,OAAO,KAAK,GAAG,CAAC,CAAA,IACd,EAAE,MAAM,KAAK,MAAM,MAAM,GACrB;wCAAE,GAAG,CAAC;wCAAE,OAAO,MAAM,KAAK;wCAAE,WAAW,MAAM,SAAS;oCAAC,IACvD;4BAER,OAAO;gCACL,OAAO;uCAAI;oCAAM;wCACf,QAAQ,MAAM,MAAM;wCACpB,OAAO,MAAM,KAAK;wCAClB,WAAW,MAAM,SAAS;oCAC5B;iCAAE;4BACJ;wBACF;oBACF;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE;oBAC5B,MAAM,CAAC,MAAM,KAAK,GAAG,KAAK,MAAM;oBAChC,iBAAiB;wBACf,QAAQ,KAAK,IAAI;wBACjB,MAAM,QAAQ,EAAE;wBAChB,MAAM,QAAQ,EAAE;wBAChB,WAAW,KAAK,GAAG;oBACrB;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,KAAK,MAAM,EAAE;oBACf,aAAa,CAAA;wBACX,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,MAAM,CAAE,MAAM;wBAChE,IAAI,UAAU;4BACZ,OAAO,KAAK,GAAG,CAAC,CAAA,IACd,EAAE,MAAM,KAAK,KAAK,MAAM,CAAE,MAAM,GAC5B;oCACE,GAAG,CAAC;oCACJ,OAAO,KAAK,MAAM,CAAE,KAAK;oCACzB,WAAW,KAAK,MAAM,CAAE,SAAS;oCACjC,QAAQ,KAAK,MAAM,CAAE,MAAM;oCAC3B,SAAS,KAAK,MAAM,CAAE,IAAI;oCAC1B,QAAQ,KAAK,MAAM,CAAE,GAAG;gCAC1B,IACA;wBAER,OAAO;4BACL,OAAO;mCAAI;gCAAM;oCACf,QAAQ,KAAK,MAAM,CAAE,MAAM;oCAC3B,OAAO,KAAK,MAAM,CAAE,KAAK;oCACzB,WAAW,KAAK,MAAM,CAAE,SAAS;oCACjC,QAAQ,KAAK,MAAM,CAAE,MAAM;oCAC3B,SAAS,KAAK,MAAM,CAAE,IAAI;oCAC1B,QAAQ,KAAK,MAAM,CAAE,GAAG;gCAC1B;6BAAE;wBACJ;oBACF;gBACF;gBACA;YAEF;gBACE,QAAQ,GAAG,CAAC,sBAAsB,SAAS;QAC/C;IACF,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,MAAM,KAAK,CAAC,IAAI,EAAE,sBAAsB,OAAO,IAAI;QAEnD,MAAM,kBAAqC;YACzC;YACA;YACA,QAAQ;YACR,YAAY,KAAK,GAAG;QACtB;QAEA,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAgB;QAEnD,IAAI,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;YAChD,IAAI;gBACF,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;oBAChC,QAAQ;oBACR;gBACF;gBACA,QAAQ,GAAG,CAAC,kBAAkB;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,iDAAiD;QAC/D;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,iBAAiB,CAAA;YACf,MAAM,eAAe,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACjD,IAAI,gBAAgB,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;gBAChE,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;oBAChC,QAAQ;oBACR,cAAc,aAAa,YAAY;gBACzC;YACF;YACA,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACvC;IACF,GAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA,eAAe,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,YAAY;QACxD;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 4205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/hooks/useWalletTracker.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { WalletData, WalletTrade, ScalpingMetrics } from '@/types/hyperliquid';\n\n// Mock data generator for demonstration\nconst generateMockWalletData = (): WalletData[] => {\n  const addresses = [\n    '******************************************',\n    '0x8ba1f109551bD432803012645Hac136c22C501e',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '0xfedcbafedcbafedcbafedcbafedcbafedcbafedcba',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n  ];\n\n  const nicknames = [\n    'Whale Hunter', 'Scalp Master', 'Quick Trader', 'Volume King',\n    'Profit Wizard', 'Speed Demon', 'Market Maker', 'Trend Rider',\n    'Risk Taker', 'Smart Money', 'Flash Trader', 'Momentum Player',\n    'Arbitrage Pro', 'Swing Master', 'Day Trader'\n  ];\n\n  return addresses.map((address, index) => {\n    const isScalper = Math.random() > 0.6; // 40% chance of being a scalper\n    const volume24h = Math.random() * 5000000 + 10000; // $10K to $5M\n    const pnl24h = (Math.random() - 0.3) * volume24h * 0.1; // -30% to +70% of 10% volume\n    const pnlAllTime = pnl24h * (Math.random() * 100 + 10); // 10-110 days worth\n    const tradeCount = isScalper ?\n      Math.floor(Math.random() * 200 + 50) : // Scalpers: 50-250 trades\n      Math.floor(Math.random() * 50 + 5);    // Others: 5-55 trades\n\n    const avgTradeSize = volume24h / tradeCount;\n    const maxTradeSize = avgTradeSize * (Math.random() * 5 + 2); // 2-7x avg\n    const minTradeSize = avgTradeSize * (Math.random() * 0.5 + 0.1); // 0.1-0.6x avg\n\n    const winRate = Math.random() * 40 + 40; // 40-80% win rate\n    const scalpingScore = isScalper ?\n      Math.floor(Math.random() * 40 + 60) : // Scalpers: 60-100\n      Math.floor(Math.random() * 60 + 10);   // Others: 10-70\n\n    const tags = [];\n    if (isScalper) tags.push('scalper');\n    if (volume24h > 1000000) tags.push('whale');\n    if (pnl24h > 50000) tags.push('profitable');\n    if (tradeCount > 100) tags.push('active');\n    if (winRate > 70) tags.push('skilled');\n\n    return {\n      address,\n      nickname: Math.random() > 0.3 ? nicknames[index % nicknames.length] : undefined,\n      totalVolume24h: volume24h,\n      totalPnl24h: pnl24h,\n      totalPnlAllTime: pnlAllTime,\n      tradeCount24h: tradeCount,\n      avgTradeSize,\n      maxTradeSize,\n      minTradeSize,\n      winRate,\n      lastTradeTime: Date.now() - Math.random() * 3600000, // Within last hour\n      isScalper,\n      scalpingScore,\n      tags\n    };\n  });\n};\n\nconst generateMockTrades = (wallets: WalletData[]): WalletTrade[] => {\n  const trades: WalletTrade[] = [];\n  const symbols = ['HYPER', 'BTC', 'ETH', 'SOL', 'AVAX'];\n  const orderTypes = ['market', 'limit', 'stop', 'stop_limit', 'take_profit', 'trailing_stop'] as const;\n  const timeInForceOptions = ['GTC', 'IOC', 'FOK', 'GTD'] as const;\n\n  wallets.forEach(wallet => {\n    const tradeCount = Math.min(wallet.tradeCount24h, 20); // Show last 20 trades max\n    let currentPosition: Record<string, number> = {}; // Track positions per symbol\n\n    for (let i = 0; i < tradeCount; i++) {\n      const symbol = symbols[Math.floor(Math.random() * symbols.length)];\n      const side = Math.random() > 0.5 ? 'buy' : 'sell';\n      const size = Math.random() * 100 + 1;\n      const price = symbol === 'HYPER' ? 28.5 + (Math.random() - 0.5) * 2 :\n                   symbol === 'BTC' ? 45000 + (Math.random() - 0.5) * 1000 :\n                   symbol === 'ETH' ? 3200 + (Math.random() - 0.5) * 200 :\n                   symbol === 'SOL' ? 100 + (Math.random() - 0.5) * 10 :\n                   50 + (Math.random() - 0.5) * 5;\n\n      const value = size * price;\n      const pnl = (Math.random() - 0.4) * value * 0.05; // -40% to +60% of 5% value\n      const fee = value * 0.001; // 0.1% fee\n\n      // Determine position side and changes\n      const currentPos = currentPosition[symbol] || 0;\n      const positionChange = side === 'buy' ? size : -size;\n      const newPosition = currentPos + positionChange;\n      currentPosition[symbol] = newPosition;\n\n      const isClosingPosition = (currentPos > 0 && side === 'sell') || (currentPos < 0 && side === 'buy');\n      const positionSide = newPosition > 0 ? 'long' : newPosition < 0 ? 'short' : 'neutral';\n\n      // Generate order details\n      const orderType = orderTypes[Math.floor(Math.random() * orderTypes.length)];\n      const isScalper = wallet.isScalper;\n      const hasStopLoss = Math.random() > (isScalper ? 0.7 : 0.4); // Scalpers use less stop losses\n      const hasTakeProfit = Math.random() > (isScalper ? 0.5 : 0.3);\n      const leverage = Math.random() > 0.6 ? Math.floor(Math.random() * 10) + 1 : undefined;\n\n      const stopLoss = hasStopLoss ?\n        (side === 'buy' ? price * (0.95 - Math.random() * 0.05) : price * (1.05 + Math.random() * 0.05)) :\n        undefined;\n\n      const takeProfit = hasTakeProfit ?\n        (side === 'buy' ? price * (1.02 + Math.random() * 0.08) : price * (0.98 - Math.random() * 0.08)) :\n        undefined;\n\n      const riskRewardRatio = (hasStopLoss && hasTakeProfit) ?\n        Math.abs((takeProfit! - price) / (stopLoss! - price)) : undefined;\n\n      const executionType = orderType === 'market' ? 'taker' :\n                           (Math.random() > 0.6 ? 'maker' : 'taker');\n\n      const positionSizing = value > 50000 ? 'aggressive' :\n                            value > 10000 ? 'moderate' : 'conservative';\n\n      trades.push({\n        walletAddress: wallet.address,\n        symbol,\n        side,\n        size,\n        price,\n        value,\n        timestamp: Date.now() - Math.random() * 86400000, // Within last 24h\n        pnl,\n        fee,\n        tradeId: `trade_${wallet.address}_${i}`,\n        orderDetails: {\n          orderType,\n          positionSide,\n          leverage,\n          stopLoss,\n          takeProfit,\n          trailingStopDistance: orderType === 'trailing_stop' ? Math.random() * 2 + 0.5 : undefined,\n          timeInForce: timeInForceOptions[Math.floor(Math.random() * timeInForceOptions.length)],\n          reduceOnly: isClosingPosition && Math.random() > 0.5,\n          postOnly: orderType === 'limit' && Math.random() > 0.7,\n          triggerPrice: ['stop', 'stop_limit', 'take_profit'].includes(orderType) ?\n            price * (0.98 + Math.random() * 0.04) : undefined,\n          executionType,\n          slippage: executionType === 'taker' ? Math.random() * 0.002 : undefined,\n          partialFills: Math.floor(Math.random() * 3),\n          averageFillPrice: price * (0.999 + Math.random() * 0.002)\n        },\n        riskManagement: {\n          hasStopLoss,\n          hasTakeProfit,\n          riskRewardRatio,\n          maxDrawdown: Math.random() * 0.15, // 0-15% max drawdown\n          positionSizing,\n          hedged: Math.random() > 0.8, // 20% chance of being hedged\n          marginUsed: leverage ? value / leverage : value,\n          liquidationPrice: leverage ?\n            (side === 'buy' ? price * (1 - 0.8/leverage) : price * (1 + 0.8/leverage)) :\n            undefined\n        },\n        relatedOrders: hasStopLoss || hasTakeProfit ?\n          [`sl_${wallet.address}_${i}`, `tp_${wallet.address}_${i}`].filter((_, idx) =>\n            (idx === 0 && hasStopLoss) || (idx === 1 && hasTakeProfit)\n          ) : undefined,\n        isClosingPosition,\n        positionChange\n      });\n    }\n  });\n\n  return trades.sort((a, b) => b.timestamp - a.timestamp);\n};\n\nexport function useWalletTracker() {\n  const [wallets, setWallets] = useState<WalletData[]>([]);\n  const [walletTrades, setWalletTrades] = useState<WalletTrade[]>([]);\n  const [trackedWallets, setTrackedWallets] = useState<Set<string>>(new Set());\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Initialize with mock data\n  useEffect(() => {\n    const mockWallets = generateMockWalletData();\n    const mockTrades = generateMockTrades(mockWallets);\n\n    setWallets(mockWallets);\n    setWalletTrades(mockTrades);\n  }, []);\n\n  const addWallet = useCallback((address: string, nickname?: string) => {\n    if (trackedWallets.has(address)) {\n      console.log('Wallet already being tracked');\n      return;\n    }\n\n    setIsLoading(true);\n\n    // Simulate API call\n    setTimeout(() => {\n      // Generate mock data for new wallet\n      const volume24h = Math.random() * 1000000 + 5000;\n      const pnl24h = (Math.random() - 0.3) * volume24h * 0.08;\n      const isScalper = Math.random() > 0.7;\n      const tradeCount = isScalper ?\n        Math.floor(Math.random() * 150 + 30) :\n        Math.floor(Math.random() * 30 + 3);\n\n      const newWallet: WalletData = {\n        address,\n        nickname,\n        totalVolume24h: volume24h,\n        totalPnl24h: pnl24h,\n        totalPnlAllTime: pnl24h * (Math.random() * 50 + 5),\n        tradeCount24h: tradeCount,\n        avgTradeSize: volume24h / tradeCount,\n        maxTradeSize: volume24h / tradeCount * (Math.random() * 4 + 2),\n        minTradeSize: volume24h / tradeCount * (Math.random() * 0.4 + 0.1),\n        winRate: Math.random() * 35 + 45,\n        lastTradeTime: Date.now() - Math.random() * 1800000,\n        isScalper,\n        scalpingScore: isScalper ? Math.floor(Math.random() * 35 + 65) : Math.floor(Math.random() * 50 + 15),\n        tags: isScalper ? ['scalper', 'tracked'] : ['tracked']\n      };\n\n      setWallets(prev => [...prev, newWallet]);\n      setTrackedWallets(prev => new Set([...prev, address]));\n      setIsLoading(false);\n    }, 1000);\n  }, [trackedWallets]);\n\n  const removeWallet = useCallback((address: string) => {\n    setWallets(prev => prev.filter(w => w.address !== address));\n    setTrackedWallets(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(address);\n      return newSet;\n    });\n    setWalletTrades(prev => prev.filter(t => t.walletAddress !== address));\n  }, []);\n\n  const getWalletTrades = useCallback((address: string): WalletTrade[] => {\n    return walletTrades.filter(trade => trade.walletAddress === address);\n  }, [walletTrades]);\n\n  const calculateScalpingMetrics = useCallback((address: string): ScalpingMetrics => {\n    const trades = getWalletTrades(address).sort((a, b) => a.timestamp - b.timestamp);\n\n    if (trades.length < 2) {\n      return {\n        avgTimeBetweenTrades: 0,\n        quickTradeRatio: 0,\n        smallTradeRatio: 0,\n        reversalRate: 0\n      };\n    }\n\n    // Calculate average time between trades\n    const timeDiffs = [];\n    for (let i = 1; i < trades.length; i++) {\n      timeDiffs.push((trades[i].timestamp - trades[i-1].timestamp) / (1000 * 60)); // minutes\n    }\n    const avgTimeBetweenTrades = timeDiffs.reduce((a, b) => a + b, 0) / timeDiffs.length;\n\n    // Quick trades (under 5 minutes)\n    const quickTrades = timeDiffs.filter(diff => diff < 5).length;\n    const quickTradeRatio = (quickTrades / timeDiffs.length) * 100;\n\n    // Small trades (under $1000)\n    const smallTrades = trades.filter(trade => trade.value < 1000).length;\n    const smallTradeRatio = (smallTrades / trades.length) * 100;\n\n    // Reversal rate (buy followed by sell or vice versa within 10 minutes)\n    let reversals = 0;\n    for (let i = 1; i < trades.length; i++) {\n      const timeDiff = (trades[i].timestamp - trades[i-1].timestamp) / (1000 * 60);\n      if (timeDiff < 10 && trades[i].side !== trades[i-1].side) {\n        reversals++;\n      }\n    }\n    const reversalRate = (reversals / (trades.length - 1)) * 100;\n\n    return {\n      avgTimeBetweenTrades,\n      quickTradeRatio,\n      smallTradeRatio,\n      reversalRate\n    };\n  }, [getWalletTrades]);\n\n  // Simulate real-time updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setWallets(prev => prev.map(wallet => {\n        // Small random updates to simulate real-time data\n        const volumeChange = (Math.random() - 0.5) * wallet.totalVolume24h * 0.01;\n        const pnlChange = (Math.random() - 0.5) * Math.abs(wallet.totalPnl24h) * 0.05;\n\n        return {\n          ...wallet,\n          totalVolume24h: Math.max(0, wallet.totalVolume24h + volumeChange),\n          totalPnl24h: wallet.totalPnl24h + pnlChange,\n          lastTradeTime: Math.random() > 0.9 ? Date.now() : wallet.lastTradeTime\n        };\n      }));\n    }, 5000); // Update every 5 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return {\n    wallets,\n    walletTrades,\n    trackedWallets,\n    isLoading,\n    addWallet,\n    removeWallet,\n    getWalletTrades,\n    calculateScalpingMetrics\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAKA,wCAAwC;AACxC,MAAM,yBAAyB;IAC7B,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY;QAChB;QAAgB;QAAgB;QAAgB;QAChD;QAAiB;QAAe;QAAgB;QAChD;QAAc;QAAe;QAAgB;QAC7C;QAAiB;QAAgB;KAClC;IAED,OAAO,UAAU,GAAG,CAAC,CAAC,SAAS;QAC7B,MAAM,YAAY,KAAK,MAAM,KAAK,KAAK,gCAAgC;QACvE,MAAM,YAAY,KAAK,MAAM,KAAK,UAAU,OAAO,cAAc;QACjE,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY,KAAK,6BAA6B;QACrF,MAAM,aAAa,SAAS,CAAC,KAAK,MAAM,KAAK,MAAM,EAAE,GAAG,oBAAoB;QAC5E,MAAM,aAAa,YACjB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MACjC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,IAAO,sBAAsB;QAE/D,MAAM,eAAe,YAAY;QACjC,MAAM,eAAe,eAAe,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,GAAG,WAAW;QACxE,MAAM,eAAe,eAAe,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,GAAG,eAAe;QAEhF,MAAM,UAAU,KAAK,MAAM,KAAK,KAAK,IAAI,kBAAkB;QAC3D,MAAM,gBAAgB,YACpB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAChC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,KAAO,gBAAgB;QAEzD,MAAM,OAAO,EAAE;QACf,IAAI,WAAW,KAAK,IAAI,CAAC;QACzB,IAAI,YAAY,SAAS,KAAK,IAAI,CAAC;QACnC,IAAI,SAAS,OAAO,KAAK,IAAI,CAAC;QAC9B,IAAI,aAAa,KAAK,KAAK,IAAI,CAAC;QAChC,IAAI,UAAU,IAAI,KAAK,IAAI,CAAC;QAE5B,OAAO;YACL;YACA,UAAU,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,QAAQ,UAAU,MAAM,CAAC,GAAG;YACtE,gBAAgB;YAChB,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf;YACA;YACA;YACA;YACA,eAAe,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK;YAC5C;YACA;YACA;QACF;IACF;AACF;AAEA,MAAM,qBAAqB,CAAC;IAC1B,MAAM,SAAwB,EAAE;IAChC,MAAM,UAAU;QAAC;QAAS;QAAO;QAAO;QAAO;KAAO;IACtD,MAAM,aAAa;QAAC;QAAU;QAAS;QAAQ;QAAc;QAAe;KAAgB;IAC5F,MAAM,qBAAqB;QAAC;QAAO;QAAO;QAAO;KAAM;IAEvD,QAAQ,OAAO,CAAC,CAAA;QACd,MAAM,aAAa,KAAK,GAAG,CAAC,OAAO,aAAa,EAAE,KAAK,0BAA0B;QACjF,IAAI,kBAA0C,CAAC,GAAG,6BAA6B;QAE/E,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACnC,MAAM,SAAS,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;YAClE,MAAM,OAAO,KAAK,MAAM,KAAK,MAAM,QAAQ;YAC3C,MAAM,OAAO,KAAK,MAAM,KAAK,MAAM;YACnC,MAAM,QAAQ,WAAW,UAAU,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACrD,WAAW,QAAQ,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,OACnD,WAAW,QAAQ,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,MAClD,WAAW,QAAQ,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KACjD,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAE1C,MAAM,QAAQ,OAAO;YACrB,MAAM,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,MAAM,2BAA2B;YAC7E,MAAM,MAAM,QAAQ,OAAO,WAAW;YAEtC,sCAAsC;YACtC,MAAM,aAAa,eAAe,CAAC,OAAO,IAAI;YAC9C,MAAM,iBAAiB,SAAS,QAAQ,OAAO,CAAC;YAChD,MAAM,cAAc,aAAa;YACjC,eAAe,CAAC,OAAO,GAAG;YAE1B,MAAM,oBAAoB,AAAC,aAAa,KAAK,SAAS,UAAY,aAAa,KAAK,SAAS;YAC7F,MAAM,eAAe,cAAc,IAAI,SAAS,cAAc,IAAI,UAAU;YAE5E,yBAAyB;YACzB,MAAM,YAAY,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;YAC3E,MAAM,YAAY,OAAO,SAAS;YAClC,MAAM,cAAc,KAAK,MAAM,KAAK,CAAC,YAAY,MAAM,GAAG,GAAG,gCAAgC;YAC7F,MAAM,gBAAgB,KAAK,MAAM,KAAK,CAAC,YAAY,MAAM,GAAG;YAC5D,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI;YAE5E,MAAM,WAAW,cACd,SAAS,QAAQ,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAC9F;YAEF,MAAM,aAAa,gBAChB,SAAS,QAAQ,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAC9F;YAEF,MAAM,kBAAkB,AAAC,eAAe,gBACtC,KAAK,GAAG,CAAC,CAAC,aAAc,KAAK,IAAI,CAAC,WAAY,KAAK,KAAK;YAE1D,MAAM,gBAAgB,cAAc,WAAW,UACzB,KAAK,MAAM,KAAK,MAAM,UAAU;YAEtD,MAAM,iBAAiB,QAAQ,QAAQ,eACjB,QAAQ,QAAQ,aAAa;YAEnD,OAAO,IAAI,CAAC;gBACV,eAAe,OAAO,OAAO;gBAC7B;gBACA;gBACA;gBACA;gBACA;gBACA,WAAW,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK;gBACxC;gBACA;gBACA,SAAS,CAAC,MAAM,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,GAAG;gBACvC,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA,sBAAsB,cAAc,kBAAkB,KAAK,MAAM,KAAK,IAAI,MAAM;oBAChF,aAAa,kBAAkB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,mBAAmB,MAAM,EAAE;oBACtF,YAAY,qBAAqB,KAAK,MAAM,KAAK;oBACjD,UAAU,cAAc,WAAW,KAAK,MAAM,KAAK;oBACnD,cAAc;wBAAC;wBAAQ;wBAAc;qBAAc,CAAC,QAAQ,CAAC,aAC3D,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI;oBAC1C;oBACA,UAAU,kBAAkB,UAAU,KAAK,MAAM,KAAK,QAAQ;oBAC9D,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oBACzC,kBAAkB,QAAQ,CAAC,QAAQ,KAAK,MAAM,KAAK,KAAK;gBAC1D;gBACA,gBAAgB;oBACd;oBACA;oBACA;oBACA,aAAa,KAAK,MAAM,KAAK;oBAC7B;oBACA,QAAQ,KAAK,MAAM,KAAK;oBACxB,YAAY,WAAW,QAAQ,WAAW;oBAC1C,kBAAkB,WACf,SAAS,QAAQ,QAAQ,CAAC,IAAI,MAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,MAAI,QAAQ,IACxE;gBACJ;gBACA,eAAe,eAAe,gBAC5B;oBAAC,CAAC,GAAG,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,GAAG;oBAAE,CAAC,GAAG,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,GAAG;iBAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MACpE,AAAC,QAAQ,KAAK,eAAiB,QAAQ,KAAK,iBAC1C;gBACN;gBACA;YACF;QACF;IACF;IAEA,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;AACxD;AAEO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;QACpB,MAAM,aAAa,mBAAmB;QAEtC,WAAW;QACX,gBAAgB;IAClB,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAiB;QAC9C,IAAI,eAAe,GAAG,CAAC,UAAU;YAC/B,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,oBAAoB;QACpB,WAAW;YACT,oCAAoC;YACpC,MAAM,YAAY,KAAK,MAAM,KAAK,UAAU;YAC5C,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACnD,MAAM,YAAY,KAAK,MAAM,KAAK;YAClC,MAAM,aAAa,YACjB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MACjC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YAElC,MAAM,YAAwB;gBAC5B;gBACA;gBACA,gBAAgB;gBAChB,aAAa;gBACb,iBAAiB,SAAS,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC;gBACjD,eAAe;gBACf,cAAc,YAAY;gBAC1B,cAAc,YAAY,aAAa,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC;gBAC7D,cAAc,YAAY,aAAa,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG;gBACjE,SAAS,KAAK,MAAM,KAAK,KAAK;gBAC9B,eAAe,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK;gBAC5C;gBACA,eAAe,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;gBACjG,MAAM,YAAY;oBAAC;oBAAW;iBAAU,GAAG;oBAAC;iBAAU;YACxD;YAEA,WAAW,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YACvC,kBAAkB,CAAA,OAAQ,IAAI,IAAI;uBAAI;oBAAM;iBAAQ;YACpD,aAAa;QACf,GAAG;IACL,GAAG;QAAC;KAAe;IAEnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;QAClD,kBAAkB,CAAA;YAChB,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,MAAM,CAAC;YACd,OAAO;QACT;QACA,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;IAC/D,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,OAAO,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,aAAa,KAAK;IAC9D,GAAG;QAAC;KAAa;IAEjB,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5C,MAAM,SAAS,gBAAgB,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;QAEhF,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,OAAO;gBACL,sBAAsB;gBACtB,iBAAiB;gBACjB,iBAAiB;gBACjB,cAAc;YAChB;QACF;QAEA,wCAAwC;QACxC,MAAM,YAAY,EAAE;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,GAAG,MAAM,CAAC,IAAE,EAAE,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,UAAU;QACzF;QACA,MAAM,uBAAuB,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,UAAU,MAAM;QAEpF,iCAAiC;QACjC,MAAM,cAAc,UAAU,MAAM,CAAC,CAAA,OAAQ,OAAO,GAAG,MAAM;QAC7D,MAAM,kBAAkB,AAAC,cAAc,UAAU,MAAM,GAAI;QAE3D,6BAA6B;QAC7B,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG,MAAM,MAAM;QACrE,MAAM,kBAAkB,AAAC,cAAc,OAAO,MAAM,GAAI;QAExD,uEAAuE;QACvE,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,GAAG,MAAM,CAAC,IAAE,EAAE,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;YAC3E,IAAI,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,IAAE,EAAE,CAAC,IAAI,EAAE;gBACxD;YACF;QACF;QACA,MAAM,eAAe,AAAC,YAAY,CAAC,OAAO,MAAM,GAAG,CAAC,IAAK;QAEzD,OAAO;YACL;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAgB;IAEpB,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,WAAW,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;oBAC1B,kDAAkD;oBAClD,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,OAAO,cAAc,GAAG;oBACrE,MAAM,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,OAAO,WAAW,IAAI;oBAEzE,OAAO;wBACL,GAAG,MAAM;wBACT,gBAAgB,KAAK,GAAG,CAAC,GAAG,OAAO,cAAc,GAAG;wBACpD,aAAa,OAAO,WAAW,GAAG;wBAClC,eAAe,KAAK,MAAM,KAAK,MAAM,KAAK,GAAG,KAAK,OAAO,aAAa;oBACxE;gBACF;QACF,GAAG,OAAO,yBAAyB;QAEnC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 4533, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { SubscriptionManager } from '@/components/SubscriptionManager';\nimport { PriceChart } from '@/components/PriceChart';\nimport { OrderBookDisplay } from '@/components/OrderBookDisplay';\nimport { WalletTracker } from '@/components/WalletTracker';\nimport { WalletAnalysis } from '@/components/WalletAnalysis';\nimport { useHyperliquidWebSocket } from '@/hooks/useHyperliquidWebSocket';\nimport { useWalletTracker } from '@/hooks/useWalletTracker';\nimport { WalletData } from '@/types/hyperliquid';\nimport { Activity, TrendingUp, BookOpen, Settings, Wallet } from 'lucide-react';\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [selectedWallet, setSelectedWallet] = useState<WalletData | null>(null);\n\n  const {\n    connectionStatus,\n    subscriptions,\n    priceData,\n    orderBookData,\n    subscribe,\n    unsubscribe,\n    connect,\n    disconnect\n  } = useHyperliquidWebSocket();\n\n  const {\n    wallets,\n    walletTrades,\n    addWallet,\n    removeWallet,\n    getWalletTrades,\n    calculateScalpingMetrics\n  } = useWalletTracker();\n\n  const handleWalletClick = (wallet: WalletData) => {\n    setSelectedWallet(wallet);\n    setActiveTab('wallet-analysis');\n  };\n\n  const handleBackToWallets = () => {\n    setSelectedWallet(null);\n    setActiveTab('wallets');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Activity className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                Hyperliquid Monitor\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className={`px-3 py-1 rounded-full text-sm font-medium ${\n                connectionStatus === 'connected'\n                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n                  : connectionStatus === 'connecting'\n                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n              }`}>\n                {connectionStatus}\n              </div>\n              <button\n                onClick={connectionStatus === 'connected' ? disconnect : connect}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                {connectionStatus === 'connected' ? 'Disconnect' : 'Connect'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className=\"bg-white dark:bg-gray-800 border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex space-x-8\">\n            {[\n              { id: 'overview', label: 'Overview', icon: TrendingUp },\n              { id: 'orderbook', label: 'Order Book', icon: BookOpen },\n              { id: 'wallets', label: 'Wallet Tracker', icon: Wallet },\n              { id: 'subscriptions', label: 'Subscriptions', icon: Settings },\n            ].map(({ id, label, icon: Icon }) => (\n              <button\n                key={id}\n                onClick={() => setActiveTab(id)}\n                className={`flex items-center px-3 py-4 text-sm font-medium border-b-2 transition-colors ${\n                  activeTab === id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <Icon className=\"h-4 w-4 mr-2\" />\n                {label}\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <PriceChart data={priceData} />\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n                  Active Subscriptions\n                </h3>\n                <div className=\"space-y-2\">\n                  {subscriptions.length === 0 ? (\n                    <p className=\"text-gray-500 dark:text-gray-400\">No active subscriptions</p>\n                  ) : (\n                    subscriptions.map((sub, index) => (\n                      <div key={index} className=\"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded\">\n                        <span className=\"font-medium text-gray-900 dark:text-white\">{sub.type}</span>\n                        <span className=\"text-sm text-gray-500 dark:text-gray-400\">{sub.symbol}</span>\n                      </div>\n                    ))\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'orderbook' && (\n          <OrderBookDisplay data={orderBookData} />\n        )}\n\n        {activeTab === 'wallets' && (\n          <WalletTracker\n            wallets={wallets}\n            walletTrades={walletTrades}\n            onAddWallet={addWallet}\n            onRemoveWallet={removeWallet}\n            onWalletClick={handleWalletClick}\n          />\n        )}\n\n        {activeTab === 'wallet-analysis' && selectedWallet && (\n          <WalletAnalysis\n            wallet={selectedWallet}\n            trades={getWalletTrades(selectedWallet.address)}\n            scalpingMetrics={calculateScalpingMetrics(selectedWallet.address)}\n            onBack={handleBackToWallets}\n          />\n        )}\n\n        {activeTab === 'subscriptions' && (\n          <SubscriptionManager\n            subscriptions={subscriptions}\n            onSubscribe={subscribe}\n            onUnsubscribe={unsubscribe}\n            connectionStatus={connectionStatus}\n          />\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAExE,MAAM,EACJ,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,aAAa,EACb,SAAS,EACT,WAAW,EACX,OAAO,EACP,UAAU,EACX,GAAG,CAAA,GAAA,uIAAA,CAAA,0BAAuB,AAAD;IAE1B,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,eAAe,EACf,wBAAwB,EACzB,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,aAAa;IACf;IAEA,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;;;;;;;0CAIlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,2CAA2C,EAC1D,qBAAqB,cACjB,sEACA,qBAAqB,eACrB,0EACA,6DACJ;kDACC;;;;;;kDAEH,8OAAC;wCACC,SAAS,qBAAqB,cAAc,aAAa;wCACzD,WAAU;kDAET,qBAAqB,cAAc,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,IAAI;gCAAY,OAAO;gCAAY,MAAM,kNAAA,CAAA,aAAU;4BAAC;4BACtD;gCAAE,IAAI;gCAAa,OAAO;gCAAc,MAAM,8MAAA,CAAA,WAAQ;4BAAC;4BACvD;gCAAE,IAAI;gCAAW,OAAO;gCAAkB,MAAM,sMAAA,CAAA,SAAM;4BAAC;4BACvD;gCAAE,IAAI;gCAAiB,OAAO;gCAAiB,MAAM,0MAAA,CAAA,WAAQ;4BAAC;yBAC/D,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC9B,8OAAC;gCAEC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,KACV,qDACA,oGACJ;;kDAEF,8OAAC;wCAAK,WAAU;;;;;;oCACf;;+BATI;;;;;;;;;;;;;;;;;;;;0BAiBf,8OAAC;gBAAK,WAAU;;oBACb,cAAc,4BACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,aAAU;oCAAC,MAAM;;;;;;8CAClB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;gDAAE,WAAU;0DAAmC;;;;;uDAEhD,cAAc,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAK,WAAU;sEAA6C,IAAI,IAAI;;;;;;sEACrE,8OAAC;4DAAK,WAAU;sEAA4C,IAAI,MAAM;;;;;;;mDAF9D;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAYvB,cAAc,6BACb,8OAAC,sIAAA,CAAA,mBAAgB;wBAAC,MAAM;;;;;;oBAGzB,cAAc,2BACb,8OAAC,mIAAA,CAAA,gBAAa;wBACZ,SAAS;wBACT,cAAc;wBACd,aAAa;wBACb,gBAAgB;wBAChB,eAAe;;;;;;oBAIlB,cAAc,qBAAqB,gCAClC,8OAAC,oIAAA,CAAA,iBAAc;wBACb,QAAQ;wBACR,QAAQ,gBAAgB,eAAe,OAAO;wBAC9C,iBAAiB,yBAAyB,eAAe,OAAO;wBAChE,QAAQ;;;;;;oBAIX,cAAc,iCACb,8OAAC,yIAAA,CAAA,sBAAmB;wBAClB,eAAe;wBACf,aAAa;wBACb,eAAe;wBACf,kBAAkB;;;;;;;;;;;;;;;;;;AAM9B"}}, {"offset": {"line": 4849, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}