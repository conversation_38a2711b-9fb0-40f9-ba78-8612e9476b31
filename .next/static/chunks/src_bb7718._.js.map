{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/SubscriptionManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { HyperliquidSubscription, ConnectionStatus } from '@/types/hyperliquid';\nimport { Plus, Trash2, Play, Pause } from 'lucide-react';\n\ninterface SubscriptionManagerProps {\n  subscriptions: HyperliquidSubscription[];\n  onSubscribe: (subscription: HyperliquidSubscription) => string;\n  onUnsubscribe: (subscriptionId: string) => void;\n  connectionStatus: ConnectionStatus;\n}\n\nconst POPULAR_SYMBOLS = ['BTC', 'ETH', 'SOL', 'AVAX', 'MATIC', 'DOGE', 'ADA', 'DOT'];\nconst SUBSCRIPTION_TYPES = [\n  { value: 'trades', label: 'Trades', description: 'Real-time trade data' },\n  { value: 'l2Book', label: 'Order Book', description: 'Level 2 order book updates' },\n  { value: 'candle', label: 'Candles', description: 'OHLCV candle data' },\n  { value: 'webData2', label: 'Web Data', description: 'General market data' },\n  { value: 'notification', label: 'Notifications', description: 'User notifications' },\n  { value: 'activeAssetCtx', label: 'Asset Context', description: 'Active asset context' }\n];\n\nconst CANDLE_INTERVALS = ['1m', '5m', '15m', '1h', '4h', '1d'];\n\nexport function SubscriptionManager({\n  subscriptions,\n  onSubscribe,\n  onUnsubscribe,\n  connectionStatus\n}: SubscriptionManagerProps) {\n  const [newSubscription, setNewSubscription] = useState<Partial<HyperliquidSubscription>>({\n    type: 'trades',\n    symbol: 'BTC'\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  const handleAddSubscription = () => {\n    if (newSubscription.type) {\n      try {\n        const subscription: HyperliquidSubscription = {\n          type: newSubscription.type,\n          ...(newSubscription.symbol && { symbol: newSubscription.symbol }),\n          ...(newSubscription.coin && { coin: newSubscription.coin }),\n          ...(newSubscription.interval && { interval: newSubscription.interval }),\n          ...(newSubscription.user && { user: newSubscription.user })\n        };\n\n        onSubscribe(subscription);\n        setNewSubscription({ type: 'trades', symbol: 'BTC' });\n        setShowAddForm(false);\n      } catch (error) {\n        console.error('Error adding subscription:', error);\n        alert('Error adding subscription. Please try again.');\n      }\n    }\n  };\n\n  const isConnected = connectionStatus === 'connected';\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            Subscription Manager\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Manage your Hyperliquid WebSocket subscriptions\n          </p>\n        </div>\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          disabled={!isConnected}\n          className={`flex items-center px-4 py-2 rounded-lg transition-colors ${\n            isConnected\n              ? 'bg-blue-600 text-white hover:bg-blue-700'\n              : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n          }`}\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Subscription\n        </button>\n      </div>\n\n      {/* Connection Status Warning */}\n      {!isConnected && (\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <Pause className=\"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2\" />\n            <p className=\"text-yellow-800 dark:text-yellow-200\">\n              WebSocket is not connected. Connect to manage subscriptions.\n            </p>\n          </div>\n        </div>\n      )}\n\n      {/* Add Subscription Form */}\n      {showAddForm && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border\">\n          <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n            Add New Subscription\n          </h3>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* Subscription Type */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Subscription Type\n              </label>\n              <select\n                value={newSubscription.type || ''}\n                onChange={(e) => setNewSubscription(prev => ({\n                  ...prev,\n                  type: e.target.value as HyperliquidSubscription['type']\n                }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                {SUBSCRIPTION_TYPES.map(type => (\n                  <option key={type.value} value={type.value}>\n                    {type.label} - {type.description}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Symbol/Coin */}\n            {(newSubscription.type === 'trades' || newSubscription.type === 'l2Book' || newSubscription.type === 'candle') && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Symbol\n                </label>\n                <div className=\"flex space-x-2\">\n                  <select\n                    value={newSubscription.symbol || ''}\n                    onChange={(e) => setNewSubscription(prev => ({ ...prev, symbol: e.target.value }))}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    {POPULAR_SYMBOLS.map(symbol => (\n                      <option key={symbol} value={symbol}>{symbol}</option>\n                    ))}\n                  </select>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Custom\"\n                    value={newSubscription.symbol || ''}\n                    onChange={(e) => setNewSubscription(prev => ({ ...prev, symbol: e.target.value }))}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Interval for candles */}\n            {newSubscription.type === 'candle' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Interval\n                </label>\n                <select\n                  value={newSubscription.interval || '1m'}\n                  onChange={(e) => setNewSubscription(prev => ({ ...prev, interval: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  {CANDLE_INTERVALS.map(interval => (\n                    <option key={interval} value={interval}>{interval}</option>\n                  ))}\n                </select>\n              </div>\n            )}\n\n            {/* User for notifications */}\n            {newSubscription.type === 'notification' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  User Address\n                </label>\n                <input\n                  type=\"text\"\n                  placeholder=\"0x...\"\n                  value={newSubscription.user || ''}\n                  onChange={(e) => setNewSubscription(prev => ({ ...prev, user: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex justify-end space-x-3 mt-6\">\n            <button\n              onClick={() => setShowAddForm(false)}\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleAddSubscription}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Add Subscription\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Active Subscriptions */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Active Subscriptions ({subscriptions.length})\n          </h3>\n        </div>\n\n        <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n          {subscriptions.length === 0 ? (\n            <div className=\"px-6 py-8 text-center\">\n              <Play className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                No active subscriptions. Add one to get started.\n              </p>\n            </div>\n          ) : (\n            subscriptions.map((subscription, index) => (\n              <div key={index} className=\"px-6 py-4 flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                      {subscription.type}\n                    </span>\n                    {subscription.symbol && (\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {subscription.symbol}\n                      </span>\n                    )}\n                    {subscription.coin && (\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {subscription.coin}\n                      </span>\n                    )}\n                    {subscription.interval && (\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {subscription.interval}\n                      </span>\n                    )}\n                    {subscription.user && (\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400 font-mono\">\n                        {subscription.user.slice(0, 8)}...\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => onUnsubscribe(`sub_${index}`)}\n                  disabled={!isConnected}\n                  className={`p-2 rounded-lg transition-colors ${\n                    isConnected\n                      ? 'text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20'\n                      : 'text-gray-400 cursor-not-allowed'\n                  }`}\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </button>\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;;;AAJA;;;AAaA,MAAM,kBAAkB;IAAC;IAAO;IAAO;IAAO;IAAQ;IAAS;IAAQ;IAAO;CAAM;AACpF,MAAM,qBAAqB;IACzB;QAAE,OAAO;QAAU,OAAO;QAAU,aAAa;IAAuB;IACxE;QAAE,OAAO;QAAU,OAAO;QAAc,aAAa;IAA6B;IAClF;QAAE,OAAO;QAAU,OAAO;QAAW,aAAa;IAAoB;IACtE;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAAsB;IAC3E;QAAE,OAAO;QAAgB,OAAO;QAAiB,aAAa;IAAqB;IACnF;QAAE,OAAO;QAAkB,OAAO;QAAiB,aAAa;IAAuB;CACxF;AAED,MAAM,mBAAmB;IAAC;IAAM;IAAM;IAAO;IAAM;IAAM;CAAK;AAEvD,SAAS,oBAAoB,EAClC,aAAa,EACb,WAAW,EACX,aAAa,EACb,gBAAgB,EACS;;IACzB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;QACvF,MAAM;QACN,QAAQ;IACV;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,wBAAwB;QAC5B,IAAI,gBAAgB,IAAI,EAAE;YACxB,IAAI;gBACF,MAAM,eAAwC;oBAC5C,MAAM,gBAAgB,IAAI;oBAC1B,GAAI,gBAAgB,MAAM,IAAI;wBAAE,QAAQ,gBAAgB,MAAM;oBAAC,CAAC;oBAChE,GAAI,gBAAgB,IAAI,IAAI;wBAAE,MAAM,gBAAgB,IAAI;oBAAC,CAAC;oBAC1D,GAAI,gBAAgB,QAAQ,IAAI;wBAAE,UAAU,gBAAgB,QAAQ;oBAAC,CAAC;oBACtE,GAAI,gBAAgB,IAAI,IAAI;wBAAE,MAAM,gBAAgB,IAAI;oBAAC,CAAC;gBAC5D;gBAEA,YAAY;gBACZ,mBAAmB;oBAAE,MAAM;oBAAU,QAAQ;gBAAM;gBACnD,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM;YACR;QACF;IACF;IAEA,MAAM,cAAc,qBAAqB;IAEzC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,6LAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,UAAU,CAAC;wBACX,WAAW,CAAC,yDAAyD,EACnE,cACI,6CACA,gDACJ;;0CAEF,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,CAAC,6BACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;;;;;;;;;;;;YAQzD,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,gBAAgB,IAAI,IAAI;wCAC/B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oDAC3C,GAAG,IAAI;oDACP,MAAM,EAAE,MAAM,CAAC,KAAK;gDACtB,CAAC;wCACD,WAAU;kDAET,mBAAmB,GAAG,CAAC,CAAA,qBACtB,6LAAC;gDAAwB,OAAO,KAAK,KAAK;;oDACvC,KAAK,KAAK;oDAAC;oDAAI,KAAK,WAAW;;+CADrB,KAAK,KAAK;;;;;;;;;;;;;;;;4BAQ5B,CAAC,gBAAgB,IAAI,KAAK,YAAY,gBAAgB,IAAI,KAAK,YAAY,gBAAgB,IAAI,KAAK,QAAQ,mBAC3G,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAO,gBAAgB,MAAM,IAAI;gDACjC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAChF,WAAU;0DAET,gBAAgB,GAAG,CAAC,CAAA,uBACnB,6LAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;0DAGjB,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,gBAAgB,MAAM,IAAI;gDACjC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAChF,WAAU;;;;;;;;;;;;;;;;;;4BAOjB,gBAAgB,IAAI,KAAK,0BACxB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,gBAAgB,QAAQ,IAAI;wCACnC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAClF,WAAU;kDAET,iBAAiB,GAAG,CAAC,CAAA,yBACpB,6LAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;;;;;;4BAOpB,gBAAgB,IAAI,KAAK,gCACxB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO,gBAAgB,IAAI,IAAI;wCAC/B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,WAAU;;;;;;;;;;;;;;;;;;kCAMlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAsD;gCAC3C,cAAc,MAAM;gCAAC;;;;;;;;;;;;kCAIhD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;mCAKlD,cAAc,GAAG,CAAC,CAAC,cAAc,sBAC/B,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,aAAa,IAAI;;;;;;gDAEnB,aAAa,MAAM,kBAClB,6LAAC;oDAAK,WAAU;8DACb,aAAa,MAAM;;;;;;gDAGvB,aAAa,IAAI,kBAChB,6LAAC;oDAAK,WAAU;8DACb,aAAa,IAAI;;;;;;gDAGrB,aAAa,QAAQ,kBACpB,6LAAC;oDAAK,WAAU;8DACb,aAAa,QAAQ;;;;;;gDAGzB,aAAa,IAAI,kBAChB,6LAAC;oDAAK,WAAU;;wDACb,aAAa,IAAI,CAAC,KAAK,CAAC,GAAG;wDAAG;;;;;;;;;;;;;;;;;;kDAMvC,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC,IAAI,EAAE,OAAO;wCAC3C,UAAU,CAAC;wCACX,WAAW,CAAC,iCAAiC,EAC3C,cACI,0DACA,oCACJ;kDAEF,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;+BAtCZ;;;;;;;;;;;;;;;;;;;;;;AA+CxB;GAtPgB;KAAA"}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/PriceChart.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\nimport { PriceData } from '@/types/hyperliquid';\nimport { TrendingUp, TrendingDown, Activity } from 'lucide-react';\n\ninterface PriceChartProps {\n  data: PriceData[];\n}\n\nexport function PriceChart({ data }: PriceChartProps) {\n  const chartData = useMemo(() => {\n    // Group data by symbol and create time series\n    const symbolData = data.reduce((acc, item) => {\n      if (!acc[item.symbol]) {\n        acc[item.symbol] = [];\n      }\n      acc[item.symbol].push({\n        timestamp: item.timestamp,\n        price: item.price,\n        time: new Date(item.timestamp).toLocaleTimeString(),\n        volume: item.volume || 0\n      });\n      return acc;\n    }, {} as Record<string, any[]>);\n\n    // Return the most recent data for the primary symbol (first one or BTC if available)\n    const primarySymbol = data.find(d => d.symbol === 'BTC')?.symbol || data[0]?.symbol;\n    if (!primarySymbol || !symbolData[primarySymbol]) return [];\n\n    return symbolData[primarySymbol]\n      .sort((a, b) => a.timestamp - b.timestamp)\n      .slice(-50); // Keep last 50 data points\n  }, [data]);\n\n  const latestData = useMemo(() => {\n    if (data.length === 0) return null;\n    \n    // Get latest data for each symbol\n    const latest = data.reduce((acc, item) => {\n      if (!acc[item.symbol] || item.timestamp > acc[item.symbol].timestamp) {\n        acc[item.symbol] = item;\n      }\n      return acc;\n    }, {} as Record<string, PriceData>);\n\n    return Object.values(latest);\n  }, [data]);\n\n  const primarySymbol = latestData?.[0]?.symbol || 'N/A';\n  const primaryPrice = latestData?.[0]?.price || 0;\n  const priceChange = latestData?.[0]?.change24h || 0;\n\n  if (!latestData || latestData.length === 0) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <Activity className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No price data available. Subscribe to trades or candles to see charts.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Price Chart\n          </h3>\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n            Real-time price movements\n          </p>\n        </div>\n        \n        {/* Primary Symbol Info */}\n        <div className=\"text-right\">\n          <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            ${primaryPrice.toLocaleString(undefined, { \n              minimumFractionDigits: 2, \n              maximumFractionDigits: 2 \n            })}\n          </div>\n          <div className={`flex items-center text-sm ${\n            priceChange >= 0 ? 'text-green-600' : 'text-red-600'\n          }`}>\n            {priceChange >= 0 ? (\n              <TrendingUp className=\"h-4 w-4 mr-1\" />\n            ) : (\n              <TrendingDown className=\"h-4 w-4 mr-1\" />\n            )}\n            {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}%\n          </div>\n        </div>\n      </div>\n\n      {/* Chart */}\n      <div className=\"h-64 mb-6\">\n        {chartData.length > 0 ? (\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <LineChart data={chartData}>\n              <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n              <XAxis \n                dataKey=\"time\" \n                tick={{ fontSize: 12 }}\n                className=\"text-gray-600 dark:text-gray-400\"\n              />\n              <YAxis \n                tick={{ fontSize: 12 }}\n                className=\"text-gray-600 dark:text-gray-400\"\n                domain={['dataMin - 10', 'dataMax + 10']}\n              />\n              <Tooltip \n                contentStyle={{\n                  backgroundColor: 'rgba(0, 0, 0, 0.8)',\n                  border: 'none',\n                  borderRadius: '8px',\n                  color: 'white'\n                }}\n                formatter={(value: number) => [\n                  `$${value.toLocaleString(undefined, { \n                    minimumFractionDigits: 2, \n                    maximumFractionDigits: 2 \n                  })}`, \n                  'Price'\n                ]}\n              />\n              <Line \n                type=\"monotone\" \n                dataKey=\"price\" \n                stroke=\"#3B82F6\" \n                strokeWidth={2}\n                dot={false}\n                activeDot={{ r: 4, fill: '#3B82F6' }}\n              />\n            </LineChart>\n          </ResponsiveContainer>\n        ) : (\n          <div className=\"flex items-center justify-center h-full\">\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              Waiting for price data...\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Symbol Grid */}\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n        {latestData.slice(0, 6).map((item) => (\n          <div key={item.symbol} className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {item.symbol}\n              </span>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                {new Date(item.timestamp).toLocaleTimeString()}\n              </span>\n            </div>\n            <div className=\"mt-1\">\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                ${item.price.toLocaleString(undefined, { \n                  minimumFractionDigits: 2, \n                  maximumFractionDigits: 6 \n                })}\n              </div>\n              {item.volume && (\n                <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Vol: {item.volume.toLocaleString()}\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAAA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AALA;;;;AAWO,SAAS,WAAW,EAAE,IAAI,EAAmB;;IAClD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE;YACxB,8CAA8C;YAC9C,MAAM,aAAa,KAAK,MAAM;4DAAC,CAAC,KAAK;oBACnC,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,EAAE;wBACrB,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,EAAE;oBACvB;oBACA,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;wBACpB,WAAW,KAAK,SAAS;wBACzB,OAAO,KAAK,KAAK;wBACjB,MAAM,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;wBACjD,QAAQ,KAAK,MAAM,IAAI;oBACzB;oBACA,OAAO;gBACT;2DAAG,CAAC;YAEJ,qFAAqF;YACrF,MAAM,gBAAgB,KAAK,IAAI;iDAAC,CAAA,IAAK,EAAE,MAAM,KAAK;iDAAQ,UAAU,IAAI,CAAC,EAAE,EAAE;YAC7E,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,cAAc,EAAE,OAAO,EAAE;YAE3D,OAAO,UAAU,CAAC,cAAc,CAC7B,IAAI;iDAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;gDACxC,KAAK,CAAC,CAAC,KAAK,2BAA2B;QAC5C;wCAAG;QAAC;KAAK;IAET,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE;YACzB,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;YAE9B,kCAAkC;YAClC,MAAM,SAAS,KAAK,MAAM;yDAAC,CAAC,KAAK;oBAC/B,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,SAAS,GAAG,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS,EAAE;wBACpE,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG;oBACrB;oBACA,OAAO;gBACT;wDAAG,CAAC;YAEJ,OAAO,OAAO,MAAM,CAAC;QACvB;yCAAG;QAAC;KAAK;IAET,MAAM,gBAAgB,YAAY,CAAC,EAAE,EAAE,UAAU;IACjD,MAAM,eAAe,YAAY,CAAC,EAAE,EAAE,SAAS;IAC/C,MAAM,cAAc,YAAY,CAAC,EAAE,EAAE,aAAa;IAElD,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAM1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAmD;oCAC9D,aAAa,cAAc,CAAC,WAAW;wCACvC,uBAAuB;wCACvB,uBAAuB;oCACzB;;;;;;;0CAEF,6LAAC;gCAAI,WAAW,CAAC,0BAA0B,EACzC,eAAe,IAAI,mBAAmB,gBACtC;;oCACC,eAAe,kBACd,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;6DAEtB,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAEzB,eAAe,IAAI,MAAM;oCAAI,YAAY,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;;0BAM3D,6LAAC;gBAAI,WAAU;0BACZ,UAAU,MAAM,GAAG,kBAClB,6LAAC,sKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,WAAU;;;;;;0CAC/C,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,MAAM;oCAAE,UAAU;gCAAG;gCACrB,WAAU;;;;;;0CAEZ,6LAAC,wJAAA,CAAA,QAAK;gCACJ,MAAM;oCAAE,UAAU;gCAAG;gCACrB,WAAU;gCACV,QAAQ;oCAAC;oCAAgB;iCAAe;;;;;;0CAE1C,6LAAC,0JAAA,CAAA,UAAO;gCACN,cAAc;oCACZ,iBAAiB;oCACjB,QAAQ;oCACR,cAAc;oCACd,OAAO;gCACT;gCACA,WAAW,CAAC,QAAkB;wCAC5B,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,WAAW;4CAClC,uBAAuB;4CACvB,uBAAuB;wCACzB,IAAI;wCACJ;qCACD;;;;;;0CAEH,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;gCACL,WAAW;oCAAE,GAAG;oCAAG,MAAM;gCAAU;;;;;;;;;;;;;;;;yCAKzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;0BAQtD,6LAAC;gBAAI,WAAU;0BACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC3B,6LAAC;wBAAsB,WAAU;;0CAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDACb,KAAK,MAAM;;;;;;kDAEd,6LAAC;wCAAK,WAAU;kDACb,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0CAGhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAsD;4CACjE,KAAK,KAAK,CAAC,cAAc,CAAC,WAAW;gDACrC,uBAAuB;gDACvB,uBAAuB;4CACzB;;;;;;;oCAED,KAAK,MAAM,kBACV,6LAAC;wCAAI,WAAU;;4CAA2C;4CAClD,KAAK,MAAM,CAAC,cAAc;;;;;;;;;;;;;;uBAlB9B,KAAK,MAAM;;;;;;;;;;;;;;;;AA2B/B;GA5KgB;KAAA"}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/OrderBookDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { OrderBookData, OrderBookLevel } from '@/types/hyperliquid';\nimport { BookOpen, TrendingUp, TrendingDown } from 'lucide-react';\n\ninterface OrderBookDisplayProps {\n  data: OrderBookData | null;\n}\n\ninterface ProcessedLevel extends OrderBookLevel {\n  total: number;\n  percentage: number;\n}\n\nexport function OrderBookDisplay({ data }: OrderBookDisplayProps) {\n  const processedData = useMemo(() => {\n    if (!data) return null;\n\n    // Process bids (buy orders) - sort by price descending\n    const processedBids: ProcessedLevel[] = data.bids\n      .sort((a, b) => b.price - a.price)\n      .slice(0, 15)\n      .reduce((acc, level, index) => {\n        const total = acc.length > 0 ? acc[acc.length - 1].total + level.size : level.size;\n        acc.push({\n          ...level,\n          total,\n          percentage: 0 // Will be calculated after we have max total\n        });\n        return acc;\n      }, [] as ProcessedLevel[]);\n\n    // Process asks (sell orders) - sort by price ascending\n    const processedAsks: ProcessedLevel[] = data.asks\n      .sort((a, b) => a.price - b.price)\n      .slice(0, 15)\n      .reduce((acc, level, index) => {\n        const total = acc.length > 0 ? acc[acc.length - 1].total + level.size : level.size;\n        acc.push({\n          ...level,\n          total,\n          percentage: 0 // Will be calculated after we have max total\n        });\n        return acc;\n      }, [] as ProcessedLevel[]);\n\n    // Calculate percentages\n    const maxBidTotal = Math.max(...processedBids.map(b => b.total), 0);\n    const maxAskTotal = Math.max(...processedAsks.map(a => a.total), 0);\n    const maxTotal = Math.max(maxBidTotal, maxAskTotal);\n\n    processedBids.forEach(bid => {\n      bid.percentage = maxTotal > 0 ? (bid.total / maxTotal) * 100 : 0;\n    });\n\n    processedAsks.forEach(ask => {\n      ask.percentage = maxTotal > 0 ? (ask.total / maxTotal) * 100 : 0;\n    });\n\n    // Calculate spread\n    const bestBid = processedBids[0]?.price || 0;\n    const bestAsk = processedAsks[0]?.price || 0;\n    const spread = bestAsk - bestBid;\n    const spreadPercentage = bestBid > 0 ? (spread / bestBid) * 100 : 0;\n\n    return {\n      bids: processedBids,\n      asks: processedAsks.reverse(), // Reverse to show highest prices at top\n      spread,\n      spreadPercentage,\n      bestBid,\n      bestAsk\n    };\n  }, [data]);\n\n  if (!data || !processedData) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-center h-96\">\n          <div className=\"text-center\">\n            <BookOpen className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No order book data available. Subscribe to l2Book to see order book.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const { bids, asks, spread, spreadPercentage, bestBid, bestAsk } = processedData;\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Order Book - {data.symbol}\n            </h3>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              Level 2 market depth\n            </p>\n          </div>\n          \n          {/* Spread Info */}\n          <div className=\"text-right\">\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">Spread</div>\n            <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              ${spread.toFixed(2)}\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {spreadPercentage.toFixed(3)}%\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Column Headers */}\n        <div className=\"grid grid-cols-3 gap-4 mb-4 text-sm font-medium text-gray-500 dark:text-gray-400\">\n          <div className=\"text-left\">Price</div>\n          <div className=\"text-center\">Size</div>\n          <div className=\"text-right\">Total</div>\n        </div>\n\n        {/* Asks (Sell Orders) */}\n        <div className=\"space-y-1 mb-4\">\n          {asks.map((ask, index) => (\n            <div key={`ask-${index}`} className=\"relative\">\n              {/* Background bar */}\n              <div \n                className=\"absolute inset-y-0 right-0 bg-red-100 dark:bg-red-900/20 rounded\"\n                style={{ width: `${ask.percentage}%` }}\n              />\n              \n              {/* Content */}\n              <div className=\"relative grid grid-cols-3 gap-4 py-1 px-2 text-sm\">\n                <div className=\"text-red-600 dark:text-red-400 font-mono\">\n                  {ask.price.toFixed(2)}\n                </div>\n                <div className=\"text-center text-gray-900 dark:text-white font-mono\">\n                  {ask.size.toFixed(4)}\n                </div>\n                <div className=\"text-right text-gray-600 dark:text-gray-400 font-mono\">\n                  {ask.total.toFixed(4)}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Spread Display */}\n        <div className=\"flex items-center justify-center py-3 my-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center space-x-4\">\n              <div className=\"flex items-center text-green-600 dark:text-green-400\">\n                <TrendingUp className=\"h-4 w-4 mr-1\" />\n                <span className=\"font-mono\">${bestBid.toFixed(2)}</span>\n              </div>\n              <div className=\"text-gray-400\">|</div>\n              <div className=\"flex items-center text-red-600 dark:text-red-400\">\n                <TrendingDown className=\"h-4 w-4 mr-1\" />\n                <span className=\"font-mono\">${bestAsk.toFixed(2)}</span>\n              </div>\n            </div>\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              Spread: ${spread.toFixed(2)} ({spreadPercentage.toFixed(3)}%)\n            </div>\n          </div>\n        </div>\n\n        {/* Bids (Buy Orders) */}\n        <div className=\"space-y-1\">\n          {bids.map((bid, index) => (\n            <div key={`bid-${index}`} className=\"relative\">\n              {/* Background bar */}\n              <div \n                className=\"absolute inset-y-0 right-0 bg-green-100 dark:bg-green-900/20 rounded\"\n                style={{ width: `${bid.percentage}%` }}\n              />\n              \n              {/* Content */}\n              <div className=\"relative grid grid-cols-3 gap-4 py-1 px-2 text-sm\">\n                <div className=\"text-green-600 dark:text-green-400 font-mono\">\n                  {bid.price.toFixed(2)}\n                </div>\n                <div className=\"text-center text-gray-900 dark:text-white font-mono\">\n                  {bid.size.toFixed(4)}\n                </div>\n                <div className=\"text-right text-gray-600 dark:text-gray-400 font-mono\">\n                  {bid.total.toFixed(4)}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Footer Info */}\n        <div className=\"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700\">\n          <div className=\"flex justify-between text-sm text-gray-500 dark:text-gray-400\">\n            <span>Last updated: {new Date(data.timestamp).toLocaleTimeString()}</span>\n            <span>Showing top 15 levels</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;;;AAJA;;;AAeO,SAAS,iBAAiB,EAAE,IAAI,EAAyB;;IAC9D,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YAC5B,IAAI,CAAC,MAAM,OAAO;YAElB,uDAAuD;YACvD,MAAM,gBAAkC,KAAK,IAAI,CAC9C,IAAI;yEAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;wEAChC,KAAK,CAAC,GAAG,IACT,MAAM;yEAAC,CAAC,KAAK,OAAO;oBACnB,MAAM,QAAQ,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,MAAM,IAAI,GAAG,MAAM,IAAI;oBAClF,IAAI,IAAI,CAAC;wBACP,GAAG,KAAK;wBACR;wBACA,YAAY,EAAE,6CAA6C;oBAC7D;oBACA,OAAO;gBACT;wEAAG,EAAE;YAEP,uDAAuD;YACvD,MAAM,gBAAkC,KAAK,IAAI,CAC9C,IAAI;yEAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;wEAChC,KAAK,CAAC,GAAG,IACT,MAAM;yEAAC,CAAC,KAAK,OAAO;oBACnB,MAAM,QAAQ,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,MAAM,IAAI,GAAG,MAAM,IAAI;oBAClF,IAAI,IAAI,CAAC;wBACP,GAAG,KAAK;wBACR;wBACA,YAAY,EAAE,6CAA6C;oBAC7D;oBACA,OAAO;gBACT;wEAAG,EAAE;YAEP,wBAAwB;YACxB,MAAM,cAAc,KAAK,GAAG,IAAI,cAAc,GAAG;uEAAC,CAAA,IAAK,EAAE,KAAK;uEAAG;YACjE,MAAM,cAAc,KAAK,GAAG,IAAI,cAAc,GAAG;uEAAC,CAAA,IAAK,EAAE,KAAK;uEAAG;YACjE,MAAM,WAAW,KAAK,GAAG,CAAC,aAAa;YAEvC,cAAc,OAAO;2DAAC,CAAA;oBACpB,IAAI,UAAU,GAAG,WAAW,IAAI,AAAC,IAAI,KAAK,GAAG,WAAY,MAAM;gBACjE;;YAEA,cAAc,OAAO;2DAAC,CAAA;oBACpB,IAAI,UAAU,GAAG,WAAW,IAAI,AAAC,IAAI,KAAK,GAAG,WAAY,MAAM;gBACjE;;YAEA,mBAAmB;YACnB,MAAM,UAAU,aAAa,CAAC,EAAE,EAAE,SAAS;YAC3C,MAAM,UAAU,aAAa,CAAC,EAAE,EAAE,SAAS;YAC3C,MAAM,SAAS,UAAU;YACzB,MAAM,mBAAmB,UAAU,IAAI,AAAC,SAAS,UAAW,MAAM;YAElE,OAAO;gBACL,MAAM;gBACN,MAAM,cAAc,OAAO;gBAC3B;gBACA;gBACA;gBACA;YACF;QACF;kDAAG;QAAC;KAAK;IAET,IAAI,CAAC,QAAQ,CAAC,eAAe;QAC3B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAsD;wCACpD,KAAK,MAAM;;;;;;;8CAE3B,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAM1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA2C;;;;;;8CAC1D,6LAAC;oCAAI,WAAU;;wCAAsD;wCACjE,OAAO,OAAO,CAAC;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;wCACZ,iBAAiB,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAY;;;;;;0CAC3B,6LAAC;gCAAI,WAAU;0CAAc;;;;;;0CAC7B,6LAAC;gCAAI,WAAU;0CAAa;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;gCAAyB,WAAU;;kDAElC,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC;wCAAC;;;;;;kDAIvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;0DAErB,6LAAC;gDAAI,WAAU;0DACZ,IAAI,IAAI,CAAC,OAAO,CAAC;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;+BAhBf,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;kCAwB5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;;wDAAY;wDAAE,QAAQ,OAAO,CAAC;;;;;;;;;;;;;sDAEhD,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;oDAAK,WAAU;;wDAAY;wDAAE,QAAQ,OAAO,CAAC;;;;;;;;;;;;;;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;;wCAAgD;wCACnD,OAAO,OAAO,CAAC;wCAAG;wCAAG,iBAAiB,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;gCAAyB,WAAU;;kDAElC,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC;wCAAC;;;;;;kDAIvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;0DAErB,6LAAC;gDAAI,WAAU;0DACZ,IAAI,IAAI,CAAC,OAAO,CAAC;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;+BAhBf,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;kCAwB5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAK;wCAAe,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;8CAChE,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAnMgB;KAAA"}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1492, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/hooks/useHyperliquidWebSocket.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport {\n  HyperliquidSubscription,\n  PriceData,\n  OrderBookData,\n  TradeData,\n  CandleData,\n  ConnectionStatus,\n  SubscriptionState,\n  HyperliquidWebSocketResponse,\n  OrderBookLevel\n} from '@/types/hyperliquid';\n\nconst HYPERLIQUID_WS_URL = 'wss://api.hyperliquid.xyz/ws';\n\nexport function useHyperliquidWebSocket() {\n  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');\n  const [subscriptions, setSubscriptions] = useState<SubscriptionState[]>([]);\n  const [priceData, setPriceData] = useState<PriceData[]>([\n    // Mock data for demonstration\n    {\n      symbol: 'BTC',\n      price: 45000,\n      timestamp: Date.now(),\n      volume: 1234567,\n      change24h: 2.5,\n      high24h: 46000,\n      low24h: 44000\n    },\n    {\n      symbol: 'ETH',\n      price: 3200,\n      timestamp: Date.now(),\n      volume: 987654,\n      change24h: -1.2,\n      high24h: 3300,\n      low24h: 3100\n    }\n  ]);\n  const [orderBookData, setOrderBookData] = useState<OrderBookData | null>({\n    symbol: 'BTC',\n    bids: [\n      { price: 44995, size: 0.5 },\n      { price: 44990, size: 1.2 },\n      { price: 44985, size: 0.8 },\n      { price: 44980, size: 2.1 },\n      { price: 44975, size: 1.5 }\n    ],\n    asks: [\n      { price: 45005, size: 0.7 },\n      { price: 45010, size: 1.1 },\n      { price: 45015, size: 0.9 },\n      { price: 45020, size: 1.8 },\n      { price: 45025, size: 1.3 }\n    ],\n    timestamp: Date.now()\n  });\n  const [tradeData, setTradeData] = useState<TradeData[]>([]);\n\n  const wsRef = useRef<WebSocket | null>(null);\n  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const subscriptionIdCounter = useRef(0);\n\n  const connect = useCallback(() => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      return;\n    }\n\n    setConnectionStatus('connecting');\n\n    try {\n      // Check if WebSocket is available in the browser\n      if (typeof WebSocket === 'undefined') {\n        console.error('WebSocket is not supported in this environment');\n        setConnectionStatus('error');\n        return;\n      }\n\n      const ws = new WebSocket(HYPERLIQUID_WS_URL);\n      wsRef.current = ws;\n\n      ws.onopen = () => {\n        console.log('WebSocket connected to Hyperliquid');\n        setConnectionStatus('connected');\n\n        // Resubscribe to active subscriptions\n        subscriptions.forEach(sub => {\n          if (sub.active) {\n            try {\n              ws.send(JSON.stringify({\n                method: 'subscribe',\n                subscription: sub.subscription\n              }));\n            } catch (error) {\n              console.error('Error sending subscription:', error);\n            }\n          }\n        });\n      };\n\n      ws.onmessage = (event) => {\n        try {\n          const message: HyperliquidWebSocketResponse = JSON.parse(event.data);\n          handleWebSocketMessage(message);\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n\n      ws.onclose = (event) => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        setConnectionStatus('disconnected');\n        wsRef.current = null;\n\n        // Auto-reconnect after 3 seconds if not manually disconnected\n        if (event.code !== 1000 && event.code !== 1001) {\n          reconnectTimeoutRef.current = setTimeout(() => {\n            console.log('Attempting to reconnect...');\n            connect();\n          }, 3000);\n        }\n      };\n\n      ws.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setConnectionStatus('error');\n      };\n\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      setConnectionStatus('error');\n    }\n  }, [subscriptions]);\n\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'Manual disconnect');\n      wsRef.current = null;\n    }\n    setConnectionStatus('disconnected');\n  }, []);\n\n  const handleWebSocketMessage = useCallback((message: HyperliquidWebSocketResponse) => {\n    const { channel, data } = message;\n\n    switch (channel) {\n      case 'trades':\n        if (data.trades) {\n          setTradeData(prev => [...prev.slice(-99), ...data.trades!].slice(-100));\n\n          // Update price data from trades\n          data.trades.forEach(trade => {\n            setPriceData(prev => {\n              const existing = prev.find(p => p.symbol === trade.symbol);\n              if (existing) {\n                return prev.map(p =>\n                  p.symbol === trade.symbol\n                    ? { ...p, price: trade.price, timestamp: trade.timestamp }\n                    : p\n                );\n              } else {\n                return [...prev, {\n                  symbol: trade.symbol,\n                  price: trade.price,\n                  timestamp: trade.timestamp\n                }];\n              }\n            });\n          });\n        }\n        break;\n\n      case 'l2Book':\n        if (data.levels && data.coin) {\n          const [bids, asks] = data.levels;\n          setOrderBookData({\n            symbol: data.coin,\n            bids: bids || [],\n            asks: asks || [],\n            timestamp: Date.now()\n          });\n        }\n        break;\n\n      case 'candle':\n        if (data.candle) {\n          setPriceData(prev => {\n            const existing = prev.find(p => p.symbol === data.candle!.symbol);\n            if (existing) {\n              return prev.map(p =>\n                p.symbol === data.candle!.symbol\n                  ? {\n                      ...p,\n                      price: data.candle!.close,\n                      timestamp: data.candle!.timestamp,\n                      volume: data.candle!.volume,\n                      high24h: data.candle!.high,\n                      low24h: data.candle!.low\n                    }\n                  : p\n              );\n            } else {\n              return [...prev, {\n                symbol: data.candle!.symbol,\n                price: data.candle!.close,\n                timestamp: data.candle!.timestamp,\n                volume: data.candle!.volume,\n                high24h: data.candle!.high,\n                low24h: data.candle!.low\n              }];\n            }\n          });\n        }\n        break;\n\n      default:\n        console.log('Unhandled channel:', channel, data);\n    }\n  }, []);\n\n  const subscribe = useCallback((subscription: HyperliquidSubscription) => {\n    const id = `sub_${subscriptionIdCounter.current++}`;\n\n    const newSubscription: SubscriptionState = {\n      id,\n      subscription,\n      active: true,\n      lastUpdate: Date.now()\n    };\n\n    setSubscriptions(prev => [...prev, newSubscription]);\n\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      try {\n        wsRef.current.send(JSON.stringify({\n          method: 'subscribe',\n          subscription\n        }));\n        console.log('Subscribed to:', subscription);\n      } catch (error) {\n        console.error('Error sending subscription:', error);\n      }\n    } else {\n      console.log('WebSocket not connected, subscription queued:', subscription);\n    }\n\n    return id;\n  }, []);\n\n  const unsubscribe = useCallback((subscriptionId: string) => {\n    setSubscriptions(prev => {\n      const subscription = prev.find(sub => sub.id === subscriptionId);\n      if (subscription && wsRef.current?.readyState === WebSocket.OPEN) {\n        wsRef.current.send(JSON.stringify({\n          method: 'unsubscribe',\n          subscription: subscription.subscription\n        }));\n      }\n      return prev.filter(sub => sub.id !== subscriptionId);\n    });\n  }, []);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n\n  return {\n    connectionStatus,\n    subscriptions: subscriptions.map(sub => sub.subscription),\n    priceData,\n    orderBookData,\n    tradeData,\n    connect,\n    disconnect,\n    subscribe,\n    unsubscribe\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAeA,MAAM,qBAAqB;AAEpB,SAAS;;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QACtD,8BAA8B;QAC9B;YACE,QAAQ;YACR,OAAO;YACP,WAAW,KAAK,GAAG;YACnB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,QAAQ;QACV;QACA;YACE,QAAQ;YACR,OAAO;YACP,WAAW,KAAK,GAAG;YACnB,QAAQ;YACR,WAAW,CAAC;YACZ,SAAS;YACT,QAAQ;QACV;KACD;IACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QACvE,QAAQ;QACR,MAAM;YACJ;gBAAE,OAAO;gBAAO,MAAM;YAAI;YAC1B;gBAAE,OAAO;gBAAO,MAAM;YAAI;YAC1B;gBAAE,OAAO;gBAAO,MAAM;YAAI;YAC1B;gBAAE,OAAO;gBAAO,MAAM;YAAI;YAC1B;gBAAE,OAAO;gBAAO,MAAM;YAAI;SAC3B;QACD,MAAM;YACJ;gBAAE,OAAO;gBAAO,MAAM;YAAI;YAC1B;gBAAE,OAAO;gBAAO,MAAM;YAAI;YAC1B;gBAAE,OAAO;gBAAO,MAAM;YAAI;YAC1B;gBAAE,OAAO;gBAAO,MAAM;YAAI;YAC1B;gBAAE,OAAO;gBAAO,MAAM;YAAI;SAC3B;QACD,WAAW,KAAK,GAAG;IACrB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAE1D,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IACvC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAC1D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAErC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAC1B,IAAI,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;gBAChD;YACF;YAEA,oBAAoB;YAEpB,IAAI;gBACF,iDAAiD;gBACjD,IAAI,OAAO,cAAc,aAAa;oBACpC,QAAQ,KAAK,CAAC;oBACd,oBAAoB;oBACpB;gBACF;gBAEA,MAAM,KAAK,IAAI,UAAU;gBACzB,MAAM,OAAO,GAAG;gBAEhB,GAAG,MAAM;oEAAG;wBACV,QAAQ,GAAG,CAAC;wBACZ,oBAAoB;wBAEpB,sCAAsC;wBACtC,cAAc,OAAO;4EAAC,CAAA;gCACpB,IAAI,IAAI,MAAM,EAAE;oCACd,IAAI;wCACF,GAAG,IAAI,CAAC,KAAK,SAAS,CAAC;4CACrB,QAAQ;4CACR,cAAc,IAAI,YAAY;wCAChC;oCACF,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,+BAA+B;oCAC/C;gCACF;4BACF;;oBACF;;gBAEA,GAAG,SAAS;oEAAG,CAAC;wBACd,IAAI;4BACF,MAAM,UAAwC,KAAK,KAAK,CAAC,MAAM,IAAI;4BACnE,uBAAuB;wBACzB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,oCAAoC;wBACpD;oBACF;;gBAEA,GAAG,OAAO;oEAAG,CAAC;wBACZ,QAAQ,GAAG,CAAC,2BAA2B,MAAM,IAAI,EAAE,MAAM,MAAM;wBAC/D,oBAAoB;wBACpB,MAAM,OAAO,GAAG;wBAEhB,8DAA8D;wBAC9D,IAAI,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,MAAM;4BAC9C,oBAAoB,OAAO,GAAG;gFAAW;oCACvC,QAAQ,GAAG,CAAC;oCACZ;gCACF;+EAAG;wBACL;oBACF;;gBAEA,GAAG,OAAO;oEAAG,CAAC;wBACZ,QAAQ,KAAK,CAAC,oBAAoB;wBAClC,oBAAoB;oBACtB;;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,oBAAoB;YACtB;QACF;uDAAG;QAAC;KAAc;IAElB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YAC7B,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,aAAa,oBAAoB,OAAO;gBACxC,oBAAoB,OAAO,GAAG;YAChC;YAEA,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM;gBAC1B,MAAM,OAAO,GAAG;YAClB;YACA,oBAAoB;QACtB;0DAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uEAAE,CAAC;YAC1C,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG;YAE1B,OAAQ;gBACN,KAAK;oBACH,IAAI,KAAK,MAAM,EAAE;wBACf;2FAAa,CAAA,OAAQ;uCAAI,KAAK,KAAK,CAAC,CAAC;uCAAQ,KAAK,MAAM;iCAAE,CAAC,KAAK,CAAC,CAAC;;wBAElE,gCAAgC;wBAChC,KAAK,MAAM,CAAC,OAAO;2FAAC,CAAA;gCAClB;mGAAa,CAAA;wCACX,MAAM,WAAW,KAAK,IAAI;oHAAC,CAAA,IAAK,EAAE,MAAM,KAAK,MAAM,MAAM;;wCACzD,IAAI,UAAU;4CACZ,OAAO,KAAK,GAAG;+GAAC,CAAA,IACd,EAAE,MAAM,KAAK,MAAM,MAAM,GACrB;wDAAE,GAAG,CAAC;wDAAE,OAAO,MAAM,KAAK;wDAAE,WAAW,MAAM,SAAS;oDAAC,IACvD;;wCAER,OAAO;4CACL,OAAO;mDAAI;gDAAM;oDACf,QAAQ,MAAM,MAAM;oDACpB,OAAO,MAAM,KAAK;oDAClB,WAAW,MAAM,SAAS;gDAC5B;6CAAE;wCACJ;oCACF;;4BACF;;oBACF;oBACA;gBAEF,KAAK;oBACH,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE;wBAC5B,MAAM,CAAC,MAAM,KAAK,GAAG,KAAK,MAAM;wBAChC,iBAAiB;4BACf,QAAQ,KAAK,IAAI;4BACjB,MAAM,QAAQ,EAAE;4BAChB,MAAM,QAAQ,EAAE;4BAChB,WAAW,KAAK,GAAG;wBACrB;oBACF;oBACA;gBAEF,KAAK;oBACH,IAAI,KAAK,MAAM,EAAE;wBACf;2FAAa,CAAA;gCACX,MAAM,WAAW,KAAK,IAAI;4GAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,MAAM,CAAE,MAAM;;gCAChE,IAAI,UAAU;oCACZ,OAAO,KAAK,GAAG;uGAAC,CAAA,IACd,EAAE,MAAM,KAAK,KAAK,MAAM,CAAE,MAAM,GAC5B;gDACE,GAAG,CAAC;gDACJ,OAAO,KAAK,MAAM,CAAE,KAAK;gDACzB,WAAW,KAAK,MAAM,CAAE,SAAS;gDACjC,QAAQ,KAAK,MAAM,CAAE,MAAM;gDAC3B,SAAS,KAAK,MAAM,CAAE,IAAI;gDAC1B,QAAQ,KAAK,MAAM,CAAE,GAAG;4CAC1B,IACA;;gCAER,OAAO;oCACL,OAAO;2CAAI;wCAAM;4CACf,QAAQ,KAAK,MAAM,CAAE,MAAM;4CAC3B,OAAO,KAAK,MAAM,CAAE,KAAK;4CACzB,WAAW,KAAK,MAAM,CAAE,SAAS;4CACjC,QAAQ,KAAK,MAAM,CAAE,MAAM;4CAC3B,SAAS,KAAK,MAAM,CAAE,IAAI;4CAC1B,QAAQ,KAAK,MAAM,CAAE,GAAG;wCAC1B;qCAAE;gCACJ;4BACF;;oBACF;oBACA;gBAEF;oBACE,QAAQ,GAAG,CAAC,sBAAsB,SAAS;YAC/C;QACF;sEAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YAC7B,MAAM,KAAK,CAAC,IAAI,EAAE,sBAAsB,OAAO,IAAI;YAEnD,MAAM,kBAAqC;gBACzC;gBACA;gBACA,QAAQ;gBACR,YAAY,KAAK,GAAG;YACtB;YAEA;kEAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAgB;;YAEnD,IAAI,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;gBAChD,IAAI;oBACF,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;wBAChC,QAAQ;wBACR;oBACF;oBACA,QAAQ,GAAG,CAAC,kBAAkB;gBAChC,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,iDAAiD;YAC/D;YAEA,OAAO;QACT;yDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YAC/B;oEAAiB,CAAA;oBACf,MAAM,eAAe,KAAK,IAAI;yFAAC,CAAA,MAAO,IAAI,EAAE,KAAK;;oBACjD,IAAI,gBAAgB,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;wBAChE,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;4BAChC,QAAQ;4BACR,cAAc,aAAa,YAAY;wBACzC;oBACF;oBACA,OAAO,KAAK,MAAM;4EAAC,CAAA,MAAO,IAAI,EAAE,KAAK;;gBACvC;;QACF;2DAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR;qDAAO;oBACL;gBACF;;QACF;4CAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA,eAAe,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,YAAY;QACxD;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA9QgB"}}, {"offset": {"line": 1840, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { SubscriptionManager } from '@/components/SubscriptionManager';\nimport { PriceChart } from '@/components/PriceChart';\nimport { OrderBookDisplay } from '@/components/OrderBookDisplay';\nimport { useHyperliquidWebSocket } from '@/hooks/useHyperliquidWebSocket';\nimport { Activity, TrendingUp, BookOpen, Settings } from 'lucide-react';\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState('overview');\n  const {\n    connectionStatus,\n    subscriptions,\n    priceData,\n    orderBookData,\n    subscribe,\n    unsubscribe,\n    connect,\n    disconnect\n  } = useHyperliquidWebSocket();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Activity className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                Hyperliquid Monitor\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className={`px-3 py-1 rounded-full text-sm font-medium ${\n                connectionStatus === 'connected'\n                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n                  : connectionStatus === 'connecting'\n                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n              }`}>\n                {connectionStatus}\n              </div>\n              <button\n                onClick={connectionStatus === 'connected' ? disconnect : connect}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                {connectionStatus === 'connected' ? 'Disconnect' : 'Connect'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className=\"bg-white dark:bg-gray-800 border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex space-x-8\">\n            {[\n              { id: 'overview', label: 'Overview', icon: TrendingUp },\n              { id: 'orderbook', label: 'Order Book', icon: BookOpen },\n              { id: 'subscriptions', label: 'Subscriptions', icon: Settings },\n            ].map(({ id, label, icon: Icon }) => (\n              <button\n                key={id}\n                onClick={() => setActiveTab(id)}\n                className={`flex items-center px-3 py-4 text-sm font-medium border-b-2 transition-colors ${\n                  activeTab === id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <Icon className=\"h-4 w-4 mr-2\" />\n                {label}\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <PriceChart data={priceData} />\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n                  Active Subscriptions\n                </h3>\n                <div className=\"space-y-2\">\n                  {subscriptions.length === 0 ? (\n                    <p className=\"text-gray-500 dark:text-gray-400\">No active subscriptions</p>\n                  ) : (\n                    subscriptions.map((sub, index) => (\n                      <div key={index} className=\"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded\">\n                        <span className=\"font-medium text-gray-900 dark:text-white\">{sub.type}</span>\n                        <span className=\"text-sm text-gray-500 dark:text-gray-400\">{sub.symbol}</span>\n                      </div>\n                    ))\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'orderbook' && (\n          <OrderBookDisplay data={orderBookData} />\n        )}\n\n        {activeTab === 'subscriptions' && (\n          <SubscriptionManager\n            subscriptions={subscriptions}\n            onSubscribe={subscribe}\n            onUnsubscribe={unsubscribe}\n            connectionStatus={connectionStatus}\n          />\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EACJ,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,aAAa,EACb,SAAS,EACT,WAAW,EACX,OAAO,EACP,UAAU,EACX,GAAG,CAAA,GAAA,0IAAA,CAAA,0BAAuB,AAAD;IAE1B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;;;;;;;0CAIlE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,2CAA2C,EAC1D,qBAAqB,cACjB,sEACA,qBAAqB,eACrB,0EACA,6DACJ;kDACC;;;;;;kDAEH,6LAAC;wCACC,SAAS,qBAAqB,cAAc,aAAa;wCACzD,WAAU;kDAET,qBAAqB,cAAc,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,IAAI;gCAAY,OAAO;gCAAY,MAAM,qNAAA,CAAA,aAAU;4BAAC;4BACtD;gCAAE,IAAI;gCAAa,OAAO;gCAAc,MAAM,iNAAA,CAAA,WAAQ;4BAAC;4BACvD;gCAAE,IAAI;gCAAiB,OAAO;gCAAiB,MAAM,6MAAA,CAAA,WAAQ;4BAAC;yBAC/D,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC9B,6LAAC;gCAEC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,KACV,qDACA,oGACJ;;kDAEF,6LAAC;wCAAK,WAAU;;;;;;oCACf;;+BATI;;;;;;;;;;;;;;;;;;;;0BAiBf,6LAAC;gBAAK,WAAU;;oBACb,cAAc,4BACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,aAAU;oCAAC,MAAM;;;;;;8CAClB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAGzE,6LAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;gDAAE,WAAU;0DAAmC;;;;;uDAEhD,cAAc,GAAG,CAAC,CAAC,KAAK,sBACtB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAK,WAAU;sEAA6C,IAAI,IAAI;;;;;;sEACrE,6LAAC;4DAAK,WAAU;sEAA4C,IAAI,MAAM;;;;;;;mDAF9D;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAYvB,cAAc,6BACb,6LAAC,yIAAA,CAAA,mBAAgB;wBAAC,MAAM;;;;;;oBAGzB,cAAc,iCACb,6LAAC,4IAAA,CAAA,sBAAmB;wBAClB,eAAe;wBACf,aAAa;wBACb,eAAe;wBACf,kBAAkB;;;;;;;;;;;;;;;;;;AAM9B;GAlHwB;;QAWlB,0IAAA,CAAA,0BAAuB;;;KAXL"}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}