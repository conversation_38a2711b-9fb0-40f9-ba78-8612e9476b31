{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/SubscriptionManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { HyperliquidSubscription, ConnectionStatus } from '@/types/hyperliquid';\nimport { Plus, Trash2, Play, Pause } from 'lucide-react';\n\ninterface SubscriptionManagerProps {\n  subscriptions: HyperliquidSubscription[];\n  onSubscribe: (subscription: HyperliquidSubscription) => string;\n  onUnsubscribe: (subscriptionId: string) => void;\n  connectionStatus: ConnectionStatus;\n}\n\nconst POPULAR_SYMBOLS = ['HYPER', 'BTC', 'ETH', 'SOL', 'AVAX', 'MATIC', 'DOGE', 'ADA', 'DOT'];\nconst SUBSCRIPTION_TYPES = [\n  { value: 'trades', label: 'Trades', description: 'Real-time trade data' },\n  { value: 'l2Book', label: 'Order Book', description: 'Level 2 order book updates' },\n  { value: 'candle', label: 'Candles', description: 'OHLCV candle data' },\n  { value: 'webData2', label: 'Web Data', description: 'General market data' },\n  { value: 'notification', label: 'Notifications', description: 'User notifications' },\n  { value: 'activeAssetCtx', label: 'Asset Context', description: 'Active asset context' }\n];\n\nconst CANDLE_INTERVALS = ['1m', '5m', '15m', '1h', '4h', '1d'];\n\nexport function SubscriptionManager({\n  subscriptions,\n  onSubscribe,\n  onUnsubscribe,\n  connectionStatus\n}: SubscriptionManagerProps) {\n  const [newSubscription, setNewSubscription] = useState<Partial<HyperliquidSubscription>>({\n    type: 'trades',\n    symbol: 'HYPER'\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  const handleAddSubscription = () => {\n    if (newSubscription.type) {\n      try {\n        const subscription: HyperliquidSubscription = {\n          type: newSubscription.type,\n          ...(newSubscription.symbol && { symbol: newSubscription.symbol }),\n          ...(newSubscription.coin && { coin: newSubscription.coin }),\n          ...(newSubscription.interval && { interval: newSubscription.interval }),\n          ...(newSubscription.user && { user: newSubscription.user })\n        };\n\n        onSubscribe(subscription);\n        setNewSubscription({ type: 'trades', symbol: 'HYPER' });\n        setShowAddForm(false);\n      } catch (error) {\n        console.error('Error adding subscription:', error);\n        alert('Error adding subscription. Please try again.');\n      }\n    }\n  };\n\n  const isConnected = connectionStatus === 'connected';\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            Subscription Manager\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Manage your Hyperliquid WebSocket subscriptions\n          </p>\n        </div>\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          disabled={!isConnected}\n          className={`flex items-center px-4 py-2 rounded-lg transition-colors ${\n            isConnected\n              ? 'bg-blue-600 text-white hover:bg-blue-700'\n              : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n          }`}\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Subscription\n        </button>\n      </div>\n\n      {/* Connection Status Warning */}\n      {!isConnected && (\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <Pause className=\"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2\" />\n            <p className=\"text-yellow-800 dark:text-yellow-200\">\n              WebSocket is not connected. Connect to manage subscriptions.\n            </p>\n          </div>\n        </div>\n      )}\n\n      {/* Add Subscription Form */}\n      {showAddForm && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border\">\n          <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n            Add New Subscription\n          </h3>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* Subscription Type */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Subscription Type\n              </label>\n              <select\n                value={newSubscription.type || ''}\n                onChange={(e) => setNewSubscription(prev => ({\n                  ...prev,\n                  type: e.target.value as HyperliquidSubscription['type']\n                }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                {SUBSCRIPTION_TYPES.map(type => (\n                  <option key={type.value} value={type.value}>\n                    {type.label} - {type.description}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Symbol/Coin */}\n            {(newSubscription.type === 'trades' || newSubscription.type === 'l2Book' || newSubscription.type === 'candle') && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Symbol\n                </label>\n                <div className=\"flex space-x-2\">\n                  <select\n                    value={newSubscription.symbol || ''}\n                    onChange={(e) => setNewSubscription(prev => ({ ...prev, symbol: e.target.value }))}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    {POPULAR_SYMBOLS.map(symbol => (\n                      <option key={symbol} value={symbol}>{symbol}</option>\n                    ))}\n                  </select>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Custom\"\n                    value={newSubscription.symbol || ''}\n                    onChange={(e) => setNewSubscription(prev => ({ ...prev, symbol: e.target.value }))}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Interval for candles */}\n            {newSubscription.type === 'candle' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Interval\n                </label>\n                <select\n                  value={newSubscription.interval || '1m'}\n                  onChange={(e) => setNewSubscription(prev => ({ ...prev, interval: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  {CANDLE_INTERVALS.map(interval => (\n                    <option key={interval} value={interval}>{interval}</option>\n                  ))}\n                </select>\n              </div>\n            )}\n\n            {/* User for notifications */}\n            {newSubscription.type === 'notification' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  User Address\n                </label>\n                <input\n                  type=\"text\"\n                  placeholder=\"0x...\"\n                  value={newSubscription.user || ''}\n                  onChange={(e) => setNewSubscription(prev => ({ ...prev, user: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex justify-end space-x-3 mt-6\">\n            <button\n              onClick={() => setShowAddForm(false)}\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleAddSubscription}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Add Subscription\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Active Subscriptions */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Active Subscriptions ({subscriptions.length})\n          </h3>\n        </div>\n\n        <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n          {subscriptions.length === 0 ? (\n            <div className=\"px-6 py-8 text-center\">\n              <Play className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                No active subscriptions. Add one to get started.\n              </p>\n            </div>\n          ) : (\n            subscriptions.map((subscription, index) => (\n              <div key={index} className=\"px-6 py-4 flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                      {subscription.type}\n                    </span>\n                    {subscription.symbol && (\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {subscription.symbol}\n                      </span>\n                    )}\n                    {subscription.coin && (\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {subscription.coin}\n                      </span>\n                    )}\n                    {subscription.interval && (\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {subscription.interval}\n                      </span>\n                    )}\n                    {subscription.user && (\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400 font-mono\">\n                        {subscription.user.slice(0, 8)}...\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => {\n                    try {\n                      onUnsubscribe(`sub_${index}`);\n                    } catch (error) {\n                      console.error('Error unsubscribing:', error);\n                    }\n                  }}\n                  disabled={!isConnected}\n                  className={`p-2 rounded-lg transition-colors ${\n                    isConnected\n                      ? 'text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20'\n                      : 'text-gray-400 cursor-not-allowed'\n                  }`}\n                  title=\"Remove subscription\"\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </button>\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;;;AAJA;;;AAaA,MAAM,kBAAkB;IAAC;IAAS;IAAO;IAAO;IAAO;IAAQ;IAAS;IAAQ;IAAO;CAAM;AAC7F,MAAM,qBAAqB;IACzB;QAAE,OAAO;QAAU,OAAO;QAAU,aAAa;IAAuB;IACxE;QAAE,OAAO;QAAU,OAAO;QAAc,aAAa;IAA6B;IAClF;QAAE,OAAO;QAAU,OAAO;QAAW,aAAa;IAAoB;IACtE;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAAsB;IAC3E;QAAE,OAAO;QAAgB,OAAO;QAAiB,aAAa;IAAqB;IACnF;QAAE,OAAO;QAAkB,OAAO;QAAiB,aAAa;IAAuB;CACxF;AAED,MAAM,mBAAmB;IAAC;IAAM;IAAM;IAAO;IAAM;IAAM;CAAK;AAEvD,SAAS,oBAAoB,EAClC,aAAa,EACb,WAAW,EACX,aAAa,EACb,gBAAgB,EACS;;IACzB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;QACvF,MAAM;QACN,QAAQ;IACV;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,wBAAwB;QAC5B,IAAI,gBAAgB,IAAI,EAAE;YACxB,IAAI;gBACF,MAAM,eAAwC;oBAC5C,MAAM,gBAAgB,IAAI;oBAC1B,GAAI,gBAAgB,MAAM,IAAI;wBAAE,QAAQ,gBAAgB,MAAM;oBAAC,CAAC;oBAChE,GAAI,gBAAgB,IAAI,IAAI;wBAAE,MAAM,gBAAgB,IAAI;oBAAC,CAAC;oBAC1D,GAAI,gBAAgB,QAAQ,IAAI;wBAAE,UAAU,gBAAgB,QAAQ;oBAAC,CAAC;oBACtE,GAAI,gBAAgB,IAAI,IAAI;wBAAE,MAAM,gBAAgB,IAAI;oBAAC,CAAC;gBAC5D;gBAEA,YAAY;gBACZ,mBAAmB;oBAAE,MAAM;oBAAU,QAAQ;gBAAQ;gBACrD,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM;YACR;QACF;IACF;IAEA,MAAM,cAAc,qBAAqB;IAEzC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,6LAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,UAAU,CAAC;wBACX,WAAW,CAAC,yDAAyD,EACnE,cACI,6CACA,gDACJ;;0CAEF,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,CAAC,6BACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;;;;;;;;;;;;YAQzD,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,gBAAgB,IAAI,IAAI;wCAC/B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oDAC3C,GAAG,IAAI;oDACP,MAAM,EAAE,MAAM,CAAC,KAAK;gDACtB,CAAC;wCACD,WAAU;kDAET,mBAAmB,GAAG,CAAC,CAAA,qBACtB,6LAAC;gDAAwB,OAAO,KAAK,KAAK;;oDACvC,KAAK,KAAK;oDAAC;oDAAI,KAAK,WAAW;;+CADrB,KAAK,KAAK;;;;;;;;;;;;;;;;4BAQ5B,CAAC,gBAAgB,IAAI,KAAK,YAAY,gBAAgB,IAAI,KAAK,YAAY,gBAAgB,IAAI,KAAK,QAAQ,mBAC3G,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAO,gBAAgB,MAAM,IAAI;gDACjC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAChF,WAAU;0DAET,gBAAgB,GAAG,CAAC,CAAA,uBACnB,6LAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;0DAGjB,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,gBAAgB,MAAM,IAAI;gDACjC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAChF,WAAU;;;;;;;;;;;;;;;;;;4BAOjB,gBAAgB,IAAI,KAAK,0BACxB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,gBAAgB,QAAQ,IAAI;wCACnC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAClF,WAAU;kDAET,iBAAiB,GAAG,CAAC,CAAA,yBACpB,6LAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;;;;;;4BAOpB,gBAAgB,IAAI,KAAK,gCACxB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO,gBAAgB,IAAI,IAAI;wCAC/B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,WAAU;;;;;;;;;;;;;;;;;;kCAMlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAsD;gCAC3C,cAAc,MAAM;gCAAC;;;;;;;;;;;;kCAIhD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;mCAKlD,cAAc,GAAG,CAAC,CAAC,cAAc,sBAC/B,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,aAAa,IAAI;;;;;;gDAEnB,aAAa,MAAM,kBAClB,6LAAC;oDAAK,WAAU;8DACb,aAAa,MAAM;;;;;;gDAGvB,aAAa,IAAI,kBAChB,6LAAC;oDAAK,WAAU;8DACb,aAAa,IAAI;;;;;;gDAGrB,aAAa,QAAQ,kBACpB,6LAAC;oDAAK,WAAU;8DACb,aAAa,QAAQ;;;;;;gDAGzB,aAAa,IAAI,kBAChB,6LAAC;oDAAK,WAAU;;wDACb,aAAa,IAAI,CAAC,KAAK,CAAC,GAAG;wDAAG;;;;;;;;;;;;;;;;;;kDAMvC,6LAAC;wCACC,SAAS;4CACP,IAAI;gDACF,cAAc,CAAC,IAAI,EAAE,OAAO;4CAC9B,EAAE,OAAO,OAAO;gDACd,QAAQ,KAAK,CAAC,wBAAwB;4CACxC;wCACF;wCACA,UAAU,CAAC;wCACX,WAAW,CAAC,iCAAiC,EAC3C,cACI,0DACA,oCACJ;wCACF,OAAM;kDAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;+BA7CZ;;;;;;;;;;;;;;;;;;;;;;AAsDxB;GA7PgB;KAAA"}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/PriceChart.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\nimport { PriceData } from '@/types/hyperliquid';\nimport { TrendingUp, TrendingDown, Activity } from 'lucide-react';\n\ninterface PriceChartProps {\n  data: PriceData[];\n}\n\nexport function PriceChart({ data }: PriceChartProps) {\n  const chartData = useMemo(() => {\n    // Group data by symbol and create time series\n    const symbolData = data.reduce((acc, item) => {\n      if (!acc[item.symbol]) {\n        acc[item.symbol] = [];\n      }\n      acc[item.symbol].push({\n        timestamp: item.timestamp,\n        price: item.price,\n        time: new Date(item.timestamp).toLocaleTimeString(),\n        volume: item.volume || 0\n      });\n      return acc;\n    }, {} as Record<string, any[]>);\n\n    // Return the most recent data for the primary symbol (HYPER first, then BTC, then first available)\n    const primarySymbol = data.find(d => d.symbol === 'HYPER')?.symbol ||\n                          data.find(d => d.symbol === 'BTC')?.symbol ||\n                          data[0]?.symbol;\n    if (!primarySymbol || !symbolData[primarySymbol]) return [];\n\n    return symbolData[primarySymbol]\n      .sort((a, b) => a.timestamp - b.timestamp)\n      .slice(-50); // Keep last 50 data points\n  }, [data]);\n\n  const latestData = useMemo(() => {\n    if (data.length === 0) return null;\n\n    // Get latest data for each symbol\n    const latest = data.reduce((acc, item) => {\n      if (!acc[item.symbol] || item.timestamp > acc[item.symbol].timestamp) {\n        acc[item.symbol] = item;\n      }\n      return acc;\n    }, {} as Record<string, PriceData>);\n\n    return Object.values(latest);\n  }, [data]);\n\n  const primarySymbol = latestData?.[0]?.symbol || 'N/A';\n  const primaryPrice = latestData?.[0]?.price || 0;\n  const priceChange = latestData?.[0]?.change24h || 0;\n\n  if (!latestData || latestData.length === 0) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <Activity className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No price data available. Subscribe to trades or candles to see charts.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Price Chart\n          </h3>\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n            Real-time price movements\n          </p>\n        </div>\n\n        {/* Primary Symbol Info */}\n        <div className=\"text-right\">\n          <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            ${primaryPrice.toLocaleString(undefined, {\n              minimumFractionDigits: 2,\n              maximumFractionDigits: 2\n            })}\n          </div>\n          <div className={`flex items-center text-sm ${\n            priceChange >= 0 ? 'text-green-600' : 'text-red-600'\n          }`}>\n            {priceChange >= 0 ? (\n              <TrendingUp className=\"h-4 w-4 mr-1\" />\n            ) : (\n              <TrendingDown className=\"h-4 w-4 mr-1\" />\n            )}\n            {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}%\n          </div>\n        </div>\n      </div>\n\n      {/* Chart */}\n      <div className=\"h-64 mb-6\">\n        {chartData.length > 0 ? (\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <LineChart data={chartData}>\n              <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n              <XAxis\n                dataKey=\"time\"\n                tick={{ fontSize: 12 }}\n                className=\"text-gray-600 dark:text-gray-400\"\n              />\n              <YAxis\n                tick={{ fontSize: 12 }}\n                className=\"text-gray-600 dark:text-gray-400\"\n                domain={['dataMin - 10', 'dataMax + 10']}\n              />\n              <Tooltip\n                contentStyle={{\n                  backgroundColor: 'rgba(0, 0, 0, 0.8)',\n                  border: 'none',\n                  borderRadius: '8px',\n                  color: 'white'\n                }}\n                formatter={(value: number) => [\n                  `$${value.toLocaleString(undefined, {\n                    minimumFractionDigits: 2,\n                    maximumFractionDigits: 2\n                  })}`,\n                  'Price'\n                ]}\n              />\n              <Line\n                type=\"monotone\"\n                dataKey=\"price\"\n                stroke=\"#3B82F6\"\n                strokeWidth={2}\n                dot={false}\n                activeDot={{ r: 4, fill: '#3B82F6' }}\n              />\n            </LineChart>\n          </ResponsiveContainer>\n        ) : (\n          <div className=\"flex items-center justify-center h-full\">\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              Waiting for price data...\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Symbol Grid */}\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n        {latestData.slice(0, 6).map((item) => (\n          <div key={item.symbol} className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {item.symbol}\n              </span>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                {new Date(item.timestamp).toLocaleTimeString()}\n              </span>\n            </div>\n            <div className=\"mt-1\">\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                ${item.price.toLocaleString(undefined, {\n                  minimumFractionDigits: 2,\n                  maximumFractionDigits: 6\n                })}\n              </div>\n              {item.volume && (\n                <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Vol: {item.volume.toLocaleString()}\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAAA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AALA;;;;AAWO,SAAS,WAAW,EAAE,IAAI,EAAmB;;IAClD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE;YACxB,8CAA8C;YAC9C,MAAM,aAAa,KAAK,MAAM;4DAAC,CAAC,KAAK;oBACnC,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,EAAE;wBACrB,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,EAAE;oBACvB;oBACA,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;wBACpB,WAAW,KAAK,SAAS;wBACzB,OAAO,KAAK,KAAK;wBACjB,MAAM,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;wBACjD,QAAQ,KAAK,MAAM,IAAI;oBACzB;oBACA,OAAO;gBACT;2DAAG,CAAC;YAEJ,mGAAmG;YACnG,MAAM,gBAAgB,KAAK,IAAI;iDAAC,CAAA,IAAK,EAAE,MAAM,KAAK;iDAAU,UACtC,KAAK,IAAI;iDAAC,CAAA,IAAK,EAAE,MAAM,KAAK;iDAAQ,UACpC,IAAI,CAAC,EAAE,EAAE;YAC/B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,cAAc,EAAE,OAAO,EAAE;YAE3D,OAAO,UAAU,CAAC,cAAc,CAC7B,IAAI;iDAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;gDACxC,KAAK,CAAC,CAAC,KAAK,2BAA2B;QAC5C;wCAAG;QAAC;KAAK;IAET,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE;YACzB,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;YAE9B,kCAAkC;YAClC,MAAM,SAAS,KAAK,MAAM;yDAAC,CAAC,KAAK;oBAC/B,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,SAAS,GAAG,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS,EAAE;wBACpE,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG;oBACrB;oBACA,OAAO;gBACT;wDAAG,CAAC;YAEJ,OAAO,OAAO,MAAM,CAAC;QACvB;yCAAG;QAAC;KAAK;IAET,MAAM,gBAAgB,YAAY,CAAC,EAAE,EAAE,UAAU;IACjD,MAAM,eAAe,YAAY,CAAC,EAAE,EAAE,SAAS;IAC/C,MAAM,cAAc,YAAY,CAAC,EAAE,EAAE,aAAa;IAElD,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAM1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAmD;oCAC9D,aAAa,cAAc,CAAC,WAAW;wCACvC,uBAAuB;wCACvB,uBAAuB;oCACzB;;;;;;;0CAEF,6LAAC;gCAAI,WAAW,CAAC,0BAA0B,EACzC,eAAe,IAAI,mBAAmB,gBACtC;;oCACC,eAAe,kBACd,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;6DAEtB,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAEzB,eAAe,IAAI,MAAM;oCAAI,YAAY,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;;0BAM3D,6LAAC;gBAAI,WAAU;0BACZ,UAAU,MAAM,GAAG,kBAClB,6LAAC,sKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,WAAU;;;;;;0CAC/C,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,MAAM;oCAAE,UAAU;gCAAG;gCACrB,WAAU;;;;;;0CAEZ,6LAAC,wJAAA,CAAA,QAAK;gCACJ,MAAM;oCAAE,UAAU;gCAAG;gCACrB,WAAU;gCACV,QAAQ;oCAAC;oCAAgB;iCAAe;;;;;;0CAE1C,6LAAC,0JAAA,CAAA,UAAO;gCACN,cAAc;oCACZ,iBAAiB;oCACjB,QAAQ;oCACR,cAAc;oCACd,OAAO;gCACT;gCACA,WAAW,CAAC,QAAkB;wCAC5B,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,WAAW;4CAClC,uBAAuB;4CACvB,uBAAuB;wCACzB,IAAI;wCACJ;qCACD;;;;;;0CAEH,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;gCACL,WAAW;oCAAE,GAAG;oCAAG,MAAM;gCAAU;;;;;;;;;;;;;;;;yCAKzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;0BAQtD,6LAAC;gBAAI,WAAU;0BACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC3B,6LAAC;wBAAsB,WAAU;;0CAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDACb,KAAK,MAAM;;;;;;kDAEd,6LAAC;wCAAK,WAAU;kDACb,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0CAGhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAsD;4CACjE,KAAK,KAAK,CAAC,cAAc,CAAC,WAAW;gDACrC,uBAAuB;gDACvB,uBAAuB;4CACzB;;;;;;;oCAED,KAAK,MAAM,kBACV,6LAAC;wCAAI,WAAU;;4CAA2C;4CAClD,KAAK,MAAM,CAAC,cAAc;;;;;;;;;;;;;;uBAlB9B,KAAK,MAAM;;;;;;;;;;;;;;;;AA2B/B;GA9KgB;KAAA"}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/OrderBookDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { OrderBookData, OrderBookLevel } from '@/types/hyperliquid';\nimport { BookOpen, TrendingUp, TrendingDown } from 'lucide-react';\n\ninterface OrderBookDisplayProps {\n  data: OrderBookData | null;\n}\n\ninterface ProcessedLevel extends OrderBookLevel {\n  total: number;\n  percentage: number;\n}\n\nexport function OrderBookDisplay({ data }: OrderBookDisplayProps) {\n  const processedData = useMemo(() => {\n    if (!data) return null;\n\n    // Process bids (buy orders) - sort by price descending\n    const processedBids: ProcessedLevel[] = data.bids\n      .sort((a, b) => b.price - a.price)\n      .slice(0, 15)\n      .reduce((acc, level, index) => {\n        const total = acc.length > 0 ? acc[acc.length - 1].total + level.size : level.size;\n        acc.push({\n          ...level,\n          total,\n          percentage: 0 // Will be calculated after we have max total\n        });\n        return acc;\n      }, [] as ProcessedLevel[]);\n\n    // Process asks (sell orders) - sort by price ascending\n    const processedAsks: ProcessedLevel[] = data.asks\n      .sort((a, b) => a.price - b.price)\n      .slice(0, 15)\n      .reduce((acc, level, index) => {\n        const total = acc.length > 0 ? acc[acc.length - 1].total + level.size : level.size;\n        acc.push({\n          ...level,\n          total,\n          percentage: 0 // Will be calculated after we have max total\n        });\n        return acc;\n      }, [] as ProcessedLevel[]);\n\n    // Calculate percentages\n    const maxBidTotal = Math.max(...processedBids.map(b => b.total), 0);\n    const maxAskTotal = Math.max(...processedAsks.map(a => a.total), 0);\n    const maxTotal = Math.max(maxBidTotal, maxAskTotal);\n\n    processedBids.forEach(bid => {\n      bid.percentage = maxTotal > 0 ? (bid.total / maxTotal) * 100 : 0;\n    });\n\n    processedAsks.forEach(ask => {\n      ask.percentage = maxTotal > 0 ? (ask.total / maxTotal) * 100 : 0;\n    });\n\n    // Calculate spread\n    const bestBid = processedBids[0]?.price || 0;\n    const bestAsk = processedAsks[0]?.price || 0;\n    const spread = bestAsk - bestBid;\n    const spreadPercentage = bestBid > 0 ? (spread / bestBid) * 100 : 0;\n\n    return {\n      bids: processedBids,\n      asks: processedAsks.reverse(), // Reverse to show highest prices at top\n      spread,\n      spreadPercentage,\n      bestBid,\n      bestAsk\n    };\n  }, [data]);\n\n  if (!data || !processedData) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-center h-96\">\n          <div className=\"text-center\">\n            <BookOpen className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No order book data available. Subscribe to l2Book to see order book.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const { bids, asks, spread, spreadPercentage, bestBid, bestAsk } = processedData;\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Order Book - {data.symbol}\n            </h3>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              Level 2 market depth\n            </p>\n          </div>\n          \n          {/* Spread Info */}\n          <div className=\"text-right\">\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">Spread</div>\n            <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              ${spread.toFixed(2)}\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {spreadPercentage.toFixed(3)}%\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Column Headers */}\n        <div className=\"grid grid-cols-3 gap-4 mb-4 text-sm font-medium text-gray-500 dark:text-gray-400\">\n          <div className=\"text-left\">Price</div>\n          <div className=\"text-center\">Size</div>\n          <div className=\"text-right\">Total</div>\n        </div>\n\n        {/* Asks (Sell Orders) */}\n        <div className=\"space-y-1 mb-4\">\n          {asks.map((ask, index) => (\n            <div key={`ask-${index}`} className=\"relative\">\n              {/* Background bar */}\n              <div \n                className=\"absolute inset-y-0 right-0 bg-red-100 dark:bg-red-900/20 rounded\"\n                style={{ width: `${ask.percentage}%` }}\n              />\n              \n              {/* Content */}\n              <div className=\"relative grid grid-cols-3 gap-4 py-1 px-2 text-sm\">\n                <div className=\"text-red-600 dark:text-red-400 font-mono\">\n                  {ask.price.toFixed(2)}\n                </div>\n                <div className=\"text-center text-gray-900 dark:text-white font-mono\">\n                  {ask.size.toFixed(4)}\n                </div>\n                <div className=\"text-right text-gray-600 dark:text-gray-400 font-mono\">\n                  {ask.total.toFixed(4)}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Spread Display */}\n        <div className=\"flex items-center justify-center py-3 my-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center space-x-4\">\n              <div className=\"flex items-center text-green-600 dark:text-green-400\">\n                <TrendingUp className=\"h-4 w-4 mr-1\" />\n                <span className=\"font-mono\">${bestBid.toFixed(2)}</span>\n              </div>\n              <div className=\"text-gray-400\">|</div>\n              <div className=\"flex items-center text-red-600 dark:text-red-400\">\n                <TrendingDown className=\"h-4 w-4 mr-1\" />\n                <span className=\"font-mono\">${bestAsk.toFixed(2)}</span>\n              </div>\n            </div>\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              Spread: ${spread.toFixed(2)} ({spreadPercentage.toFixed(3)}%)\n            </div>\n          </div>\n        </div>\n\n        {/* Bids (Buy Orders) */}\n        <div className=\"space-y-1\">\n          {bids.map((bid, index) => (\n            <div key={`bid-${index}`} className=\"relative\">\n              {/* Background bar */}\n              <div \n                className=\"absolute inset-y-0 right-0 bg-green-100 dark:bg-green-900/20 rounded\"\n                style={{ width: `${bid.percentage}%` }}\n              />\n              \n              {/* Content */}\n              <div className=\"relative grid grid-cols-3 gap-4 py-1 px-2 text-sm\">\n                <div className=\"text-green-600 dark:text-green-400 font-mono\">\n                  {bid.price.toFixed(2)}\n                </div>\n                <div className=\"text-center text-gray-900 dark:text-white font-mono\">\n                  {bid.size.toFixed(4)}\n                </div>\n                <div className=\"text-right text-gray-600 dark:text-gray-400 font-mono\">\n                  {bid.total.toFixed(4)}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Footer Info */}\n        <div className=\"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700\">\n          <div className=\"flex justify-between text-sm text-gray-500 dark:text-gray-400\">\n            <span>Last updated: {new Date(data.timestamp).toLocaleTimeString()}</span>\n            <span>Showing top 15 levels</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;;;AAJA;;;AAeO,SAAS,iBAAiB,EAAE,IAAI,EAAyB;;IAC9D,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YAC5B,IAAI,CAAC,MAAM,OAAO;YAElB,uDAAuD;YACvD,MAAM,gBAAkC,KAAK,IAAI,CAC9C,IAAI;yEAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;wEAChC,KAAK,CAAC,GAAG,IACT,MAAM;yEAAC,CAAC,KAAK,OAAO;oBACnB,MAAM,QAAQ,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,MAAM,IAAI,GAAG,MAAM,IAAI;oBAClF,IAAI,IAAI,CAAC;wBACP,GAAG,KAAK;wBACR;wBACA,YAAY,EAAE,6CAA6C;oBAC7D;oBACA,OAAO;gBACT;wEAAG,EAAE;YAEP,uDAAuD;YACvD,MAAM,gBAAkC,KAAK,IAAI,CAC9C,IAAI;yEAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;wEAChC,KAAK,CAAC,GAAG,IACT,MAAM;yEAAC,CAAC,KAAK,OAAO;oBACnB,MAAM,QAAQ,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,MAAM,IAAI,GAAG,MAAM,IAAI;oBAClF,IAAI,IAAI,CAAC;wBACP,GAAG,KAAK;wBACR;wBACA,YAAY,EAAE,6CAA6C;oBAC7D;oBACA,OAAO;gBACT;wEAAG,EAAE;YAEP,wBAAwB;YACxB,MAAM,cAAc,KAAK,GAAG,IAAI,cAAc,GAAG;uEAAC,CAAA,IAAK,EAAE,KAAK;uEAAG;YACjE,MAAM,cAAc,KAAK,GAAG,IAAI,cAAc,GAAG;uEAAC,CAAA,IAAK,EAAE,KAAK;uEAAG;YACjE,MAAM,WAAW,KAAK,GAAG,CAAC,aAAa;YAEvC,cAAc,OAAO;2DAAC,CAAA;oBACpB,IAAI,UAAU,GAAG,WAAW,IAAI,AAAC,IAAI,KAAK,GAAG,WAAY,MAAM;gBACjE;;YAEA,cAAc,OAAO;2DAAC,CAAA;oBACpB,IAAI,UAAU,GAAG,WAAW,IAAI,AAAC,IAAI,KAAK,GAAG,WAAY,MAAM;gBACjE;;YAEA,mBAAmB;YACnB,MAAM,UAAU,aAAa,CAAC,EAAE,EAAE,SAAS;YAC3C,MAAM,UAAU,aAAa,CAAC,EAAE,EAAE,SAAS;YAC3C,MAAM,SAAS,UAAU;YACzB,MAAM,mBAAmB,UAAU,IAAI,AAAC,SAAS,UAAW,MAAM;YAElE,OAAO;gBACL,MAAM;gBACN,MAAM,cAAc,OAAO;gBAC3B;gBACA;gBACA;gBACA;YACF;QACF;kDAAG;QAAC;KAAK;IAET,IAAI,CAAC,QAAQ,CAAC,eAAe;QAC3B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAsD;wCACpD,KAAK,MAAM;;;;;;;8CAE3B,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAM1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA2C;;;;;;8CAC1D,6LAAC;oCAAI,WAAU;;wCAAsD;wCACjE,OAAO,OAAO,CAAC;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;wCACZ,iBAAiB,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAY;;;;;;0CAC3B,6LAAC;gCAAI,WAAU;0CAAc;;;;;;0CAC7B,6LAAC;gCAAI,WAAU;0CAAa;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;gCAAyB,WAAU;;kDAElC,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC;wCAAC;;;;;;kDAIvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;0DAErB,6LAAC;gDAAI,WAAU;0DACZ,IAAI,IAAI,CAAC,OAAO,CAAC;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;+BAhBf,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;kCAwB5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;;wDAAY;wDAAE,QAAQ,OAAO,CAAC;;;;;;;;;;;;;sDAEhD,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;oDAAK,WAAU;;wDAAY;wDAAE,QAAQ,OAAO,CAAC;;;;;;;;;;;;;;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;;wCAAgD;wCACnD,OAAO,OAAO,CAAC;wCAAG;wCAAG,iBAAiB,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;gCAAyB,WAAU;;kDAElC,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC;wCAAC;;;;;;kDAIvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;0DAErB,6LAAC;gDAAI,WAAU;0DACZ,IAAI,IAAI,CAAC,OAAO,CAAC;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;+BAhBf,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;kCAwB5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAK;wCAAe,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;8CAChE,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAnMgB;KAAA"}}, {"offset": {"line": 1496, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/WalletTracker.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport { WalletData, WalletFilter, WalletTrade } from '@/types/hyperliquid';\nimport {\n  Wallet,\n  Filter,\n  TrendingUp,\n  TrendingDown,\n  Clock,\n  DollarSign,\n  Target,\n  Zap,\n  Star,\n  Eye,\n  EyeOff,\n  Plus\n} from 'lucide-react';\n\ninterface WalletTrackerProps {\n  wallets: WalletData[];\n  walletTrades: WalletTrade[];\n  liveActivity?: any[]; // LiveTradingActivity[] - optional for backwards compatibility\n  onAddWallet: (address: string, nickname?: string) => void;\n  onRemoveWallet: (address: string) => void;\n  onWalletClick: (wallet: WalletData) => void;\n}\n\nconst DEFAULT_FILTER: WalletFilter = {\n  sortBy: 'pnl24h',\n  sortOrder: 'desc',\n  limit: 10\n};\n\nexport function WalletTracker({\n  wallets,\n  walletTrades,\n  liveActivity = [],\n  onAddWallet,\n  onRemoveWallet,\n  onWalletClick\n}: WalletTrackerProps) {\n  const [filter, setFilter] = useState<WalletFilter>(DEFAULT_FILTER);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newWalletAddress, setNewWalletAddress] = useState('');\n  const [newWalletNickname, setNewWalletNickname] = useState('');\n  const [watchedWallets, setWatchedWallets] = useState<Set<string>>(new Set());\n\n  const filteredWallets = useMemo(() => {\n    let filtered = wallets.filter(wallet => {\n      if (filter.minVolume24h && wallet.totalVolume24h < filter.minVolume24h) return false;\n      if (filter.maxVolume24h && wallet.totalVolume24h > filter.maxVolume24h) return false;\n      if (filter.minPnl24h && wallet.totalPnl24h < filter.minPnl24h) return false;\n      if (filter.maxPnl24h && wallet.totalPnl24h > filter.maxPnl24h) return false;\n      if (filter.minTradeSize && wallet.avgTradeSize < filter.minTradeSize) return false;\n      if (filter.maxTradeSize && wallet.maxTradeSize > filter.maxTradeSize) return false;\n      if (filter.minWinRate && wallet.winRate < filter.minWinRate) return false;\n      if (filter.maxWinRate && wallet.winRate > filter.maxWinRate) return false;\n      if (filter.onlyScalpers && !wallet.isScalper) return false;\n      if (filter.minScalpingScore && wallet.scalpingScore < filter.minScalpingScore) return false;\n      if (filter.tags && filter.tags.length > 0) {\n        const hasTag = filter.tags.some(tag => wallet.tags.includes(tag));\n        if (!hasTag) return false;\n      }\n      return true;\n    });\n\n    // Sort\n    filtered.sort((a, b) => {\n      let aValue: number, bValue: number;\n      switch (filter.sortBy) {\n        case 'volume24h':\n          aValue = a.totalVolume24h;\n          bValue = b.totalVolume24h;\n          break;\n        case 'pnl24h':\n          aValue = a.totalPnl24h;\n          bValue = b.totalPnl24h;\n          break;\n        case 'pnlAllTime':\n          aValue = a.totalPnlAllTime;\n          bValue = b.totalPnlAllTime;\n          break;\n        case 'winRate':\n          aValue = a.winRate;\n          bValue = b.winRate;\n          break;\n        case 'tradeCount':\n          aValue = a.tradeCount24h;\n          bValue = b.tradeCount24h;\n          break;\n        case 'scalpingScore':\n          aValue = a.scalpingScore;\n          bValue = b.scalpingScore;\n          break;\n        default:\n          aValue = a.totalPnl24h;\n          bValue = b.totalPnl24h;\n      }\n\n      return filter.sortOrder === 'desc' ? bValue - aValue : aValue - bValue;\n    });\n\n    return filtered.slice(0, filter.limit);\n  }, [wallets, filter]);\n\n  const handleAddWallet = () => {\n    if (newWalletAddress.trim()) {\n      onAddWallet(newWalletAddress.trim(), newWalletNickname.trim() || undefined);\n      setNewWalletAddress('');\n      setNewWalletNickname('');\n      setShowAddForm(false);\n    }\n  };\n\n  const toggleWatchWallet = (address: string) => {\n    const newWatched = new Set(watchedWallets);\n    if (newWatched.has(address)) {\n      newWatched.delete(address);\n    } else {\n      newWatched.add(address);\n    }\n    setWatchedWallets(newWatched);\n  };\n\n  const formatCurrency = (value: number) => {\n    if (Math.abs(value) >= 1000000) {\n      return `$${(value / 1000000).toFixed(2)}M`;\n    } else if (Math.abs(value) >= 1000) {\n      return `$${(value / 1000).toFixed(1)}K`;\n    } else {\n      return `$${value.toFixed(2)}`;\n    }\n  };\n\n  const formatAddress = (address: string) => {\n    return `${address.slice(0, 6)}...${address.slice(-4)}`;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            Wallet Tracker\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Monitor top traders and scalpers on Hyperliquid\n          </p>\n        </div>\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Wallet\n        </button>\n      </div>\n\n      {/* Add Wallet Form */}\n      {showAddForm && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border\">\n          <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n            Add Wallet to Track\n          </h3>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Wallet Address\n              </label>\n              <input\n                type=\"text\"\n                placeholder=\"0x...\"\n                value={newWalletAddress}\n                onChange={(e) => setNewWalletAddress(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Nickname (Optional)\n              </label>\n              <input\n                type=\"text\"\n                placeholder=\"e.g., Whale #1\"\n                value={newWalletNickname}\n                onChange={(e) => setNewWalletNickname(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex justify-end space-x-3 mt-6\">\n            <button\n              onClick={() => setShowAddForm(false)}\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleAddWallet}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Add Wallet\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center mb-4\">\n          <Filter className=\"h-5 w-5 text-gray-500 mr-2\" />\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Filters</h3>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {/* Sort By */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Sort By\n            </label>\n            <select\n              value={filter.sortBy}\n              onChange={(e) => setFilter(prev => ({\n                ...prev,\n                sortBy: e.target.value as WalletFilter['sortBy']\n              }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"pnl24h\">24h PnL</option>\n              <option value=\"pnlAllTime\">All Time PnL</option>\n              <option value=\"volume24h\">24h Volume</option>\n              <option value=\"winRate\">Win Rate</option>\n              <option value=\"tradeCount\">Trade Count</option>\n              <option value=\"scalpingScore\">Scalping Score</option>\n            </select>\n          </div>\n\n          {/* Limit */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Show Top\n            </label>\n            <select\n              value={filter.limit}\n              onChange={(e) => setFilter(prev => ({\n                ...prev,\n                limit: parseInt(e.target.value)\n              }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value={5}>Top 5</option>\n              <option value={10}>Top 10</option>\n              <option value={25}>Top 25</option>\n              <option value={50}>Top 50</option>\n              <option value={100}>Top 100</option>\n            </select>\n          </div>\n\n          {/* Max Trade Size */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Max Trade Size\n            </label>\n            <select\n              value={filter.maxTradeSize || ''}\n              onChange={(e) => setFilter(prev => ({\n                ...prev,\n                maxTradeSize: e.target.value ? parseInt(e.target.value) : undefined\n              }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"\">No Limit</option>\n              <option value={1000}>$1K</option>\n              <option value={5000}>$5K</option>\n              <option value={10000}>$10K</option>\n              <option value={50000}>$50K</option>\n              <option value={100000}>$100K</option>\n            </select>\n          </div>\n\n          {/* Scalpers Only */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Trader Type\n            </label>\n            <select\n              value={filter.onlyScalpers ? 'scalpers' : 'all'}\n              onChange={(e) => setFilter(prev => ({\n                ...prev,\n                onlyScalpers: e.target.value === 'scalpers'\n              }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"all\">All Traders</option>\n              <option value=\"scalpers\">Scalpers Only</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Wallet List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Top Wallets ({filteredWallets.length})\n          </h3>\n        </div>\n\n        <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n          {filteredWallets.length === 0 ? (\n            <div className=\"px-6 py-8 text-center\">\n              <Wallet className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                No wallets found matching your filters.\n              </p>\n            </div>\n          ) : (\n            filteredWallets.map((wallet, index) => (\n              <div\n                key={wallet.address}\n                className=\"px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors\"\n                onClick={() => onWalletClick(wallet)}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n                        #{index + 1}\n                      </span>\n                      {wallet.isScalper && (\n                        <Zap className=\"h-4 w-4 text-yellow-500\" title=\"Scalper\" />\n                      )}\n                    </div>\n\n                    <div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"font-mono text-sm text-gray-900 dark:text-white\">\n                          {formatAddress(wallet.address)}\n                        </span>\n                        {wallet.nickname && (\n                          <span className=\"text-sm text-blue-600 dark:text-blue-400\">\n                            ({wallet.nickname})\n                          </span>\n                        )}\n                      </div>\n\n                      <div className=\"flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400\">\n                        <span className=\"flex items-center\">\n                          <DollarSign className=\"h-3 w-3 mr-1\" />\n                          Vol: {formatCurrency(wallet.totalVolume24h)}\n                        </span>\n                        <span className=\"flex items-center\">\n                          <Target className=\"h-3 w-3 mr-1\" />\n                          Win: {wallet.winRate.toFixed(1)}%\n                        </span>\n                        <span className=\"flex items-center\">\n                          <Clock className=\"h-3 w-3 mr-1\" />\n                          {wallet.tradeCount24h} trades\n                        </span>\n                        {wallet.isScalper && (\n                          <span className=\"flex items-center text-yellow-600 dark:text-yellow-400\">\n                            <Zap className=\"h-3 w-3 mr-1\" />\n                            Score: {wallet.scalpingScore}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"text-right\">\n                      <div className={`text-lg font-semibold ${\n                        wallet.totalPnl24h >= 0\n                          ? 'text-green-600 dark:text-green-400'\n                          : 'text-red-600 dark:text-red-400'\n                      }`}>\n                        {wallet.totalPnl24h >= 0 ? '+' : ''}{formatCurrency(wallet.totalPnl24h)}\n                      </div>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        24h PnL\n                      </div>\n                    </div>\n\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => toggleWatchWallet(wallet.address)}\n                        className={`p-2 rounded-lg transition-colors ${\n                          watchedWallets.has(wallet.address)\n                            ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'\n                            : 'text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'\n                        }`}\n                        title={watchedWallets.has(wallet.address) ? 'Stop watching' : 'Watch wallet'}\n                      >\n                        {watchedWallets.has(wallet.address) ? (\n                          <Eye className=\"h-4 w-4\" />\n                        ) : (\n                          <EyeOff className=\"h-4 w-4\" />\n                        )}\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;AA4BA,MAAM,iBAA+B;IACnC,QAAQ;IACR,WAAW;IACX,OAAO;AACT;AAEO,SAAS,cAAc,EAC5B,OAAO,EACP,YAAY,EACZ,eAAe,EAAE,EACjB,WAAW,EACX,cAAc,EACd,aAAa,EACM;;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEtE,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YAC9B,IAAI,WAAW,QAAQ,MAAM;mEAAC,CAAA;oBAC5B,IAAI,OAAO,YAAY,IAAI,OAAO,cAAc,GAAG,OAAO,YAAY,EAAE,OAAO;oBAC/E,IAAI,OAAO,YAAY,IAAI,OAAO,cAAc,GAAG,OAAO,YAAY,EAAE,OAAO;oBAC/E,IAAI,OAAO,SAAS,IAAI,OAAO,WAAW,GAAG,OAAO,SAAS,EAAE,OAAO;oBACtE,IAAI,OAAO,SAAS,IAAI,OAAO,WAAW,GAAG,OAAO,SAAS,EAAE,OAAO;oBACtE,IAAI,OAAO,YAAY,IAAI,OAAO,YAAY,GAAG,OAAO,YAAY,EAAE,OAAO;oBAC7E,IAAI,OAAO,YAAY,IAAI,OAAO,YAAY,GAAG,OAAO,YAAY,EAAE,OAAO;oBAC7E,IAAI,OAAO,UAAU,IAAI,OAAO,OAAO,GAAG,OAAO,UAAU,EAAE,OAAO;oBACpE,IAAI,OAAO,UAAU,IAAI,OAAO,OAAO,GAAG,OAAO,UAAU,EAAE,OAAO;oBACpE,IAAI,OAAO,YAAY,IAAI,CAAC,OAAO,SAAS,EAAE,OAAO;oBACrD,IAAI,OAAO,gBAAgB,IAAI,OAAO,aAAa,GAAG,OAAO,gBAAgB,EAAE,OAAO;oBACtF,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;wBACzC,MAAM,SAAS,OAAO,IAAI,CAAC,IAAI;sFAAC,CAAA,MAAO,OAAO,IAAI,CAAC,QAAQ,CAAC;;wBAC5D,IAAI,CAAC,QAAQ,OAAO;oBACtB;oBACA,OAAO;gBACT;;YAEA,OAAO;YACP,SAAS,IAAI;0DAAC,CAAC,GAAG;oBAChB,IAAI,QAAgB;oBACpB,OAAQ,OAAO,MAAM;wBACnB,KAAK;4BACH,SAAS,EAAE,cAAc;4BACzB,SAAS,EAAE,cAAc;4BACzB;wBACF,KAAK;4BACH,SAAS,EAAE,WAAW;4BACtB,SAAS,EAAE,WAAW;4BACtB;wBACF,KAAK;4BACH,SAAS,EAAE,eAAe;4BAC1B,SAAS,EAAE,eAAe;4BAC1B;wBACF,KAAK;4BACH,SAAS,EAAE,OAAO;4BAClB,SAAS,EAAE,OAAO;4BAClB;wBACF,KAAK;4BACH,SAAS,EAAE,aAAa;4BACxB,SAAS,EAAE,aAAa;4BACxB;wBACF,KAAK;4BACH,SAAS,EAAE,aAAa;4BACxB,SAAS,EAAE,aAAa;4BACxB;wBACF;4BACE,SAAS,EAAE,WAAW;4BACtB,SAAS,EAAE,WAAW;oBAC1B;oBAEA,OAAO,OAAO,SAAS,KAAK,SAAS,SAAS,SAAS,SAAS;gBAClE;;YAEA,OAAO,SAAS,KAAK,CAAC,GAAG,OAAO,KAAK;QACvC;iDAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,kBAAkB;QACtB,IAAI,iBAAiB,IAAI,IAAI;YAC3B,YAAY,iBAAiB,IAAI,IAAI,kBAAkB,IAAI,MAAM;YACjE,oBAAoB;YACpB,qBAAqB;YACrB,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,IAAI,IAAI;QAC3B,IAAI,WAAW,GAAG,CAAC,UAAU;YAC3B,WAAW,MAAM,CAAC;QACpB,OAAO;YACL,WAAW,GAAG,CAAC;QACjB;QACA,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,GAAG,CAAC,UAAU,SAAS;YAC9B,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO,IAAI,KAAK,GAAG,CAAC,UAAU,MAAM;YAClC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO;YACL,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;QAC/B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,IAAI;IACxD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,6LAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wCACpD,WAAU;;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;;;;;;;kCAGtE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,OAAO,MAAM;wCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAClC,GAAG,IAAI;oDACP,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACxB,CAAC;wCACD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,6LAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,6LAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,6LAAC;gDAAO,OAAM;0DAAgB;;;;;;;;;;;;;;;;;;0CAKlC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,OAAO,KAAK;wCACnB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAClC,GAAG,IAAI;oDACP,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;gDAChC,CAAC;wCACD,WAAU;;0DAEV,6LAAC;gDAAO,OAAO;0DAAG;;;;;;0DAClB,6LAAC;gDAAO,OAAO;0DAAI;;;;;;0DACnB,6LAAC;gDAAO,OAAO;0DAAI;;;;;;0DACnB,6LAAC;gDAAO,OAAO;0DAAI;;;;;;0DACnB,6LAAC;gDAAO,OAAO;0DAAK;;;;;;;;;;;;;;;;;;0CAKxB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,OAAO,YAAY,IAAI;wCAC9B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAClC,GAAG,IAAI;oDACP,cAAc,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC5D,CAAC;wCACD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,6LAAC;gDAAO,OAAO;0DAAM;;;;;;0DACrB,6LAAC;gDAAO,OAAO;0DAAM;;;;;;0DACrB,6LAAC;gDAAO,OAAO;0DAAO;;;;;;0DACtB,6LAAC;gDAAO,OAAO;0DAAO;;;;;;0DACtB,6LAAC;gDAAO,OAAO;0DAAQ;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,OAAO,YAAY,GAAG,aAAa;wCAC1C,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAClC,GAAG,IAAI;oDACP,cAAc,EAAE,MAAM,CAAC,KAAK,KAAK;gDACnC,CAAC;wCACD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAsD;gCACpD,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;kCAIzC,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;mCAKlD,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE7B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAuD;gEACnE,QAAQ;;;;;;;wDAEX,OAAO,SAAS,kBACf,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;4DAA0B,OAAM;;;;;;;;;;;;8DAInD,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,cAAc,OAAO,OAAO;;;;;;gEAE9B,OAAO,QAAQ,kBACd,6LAAC;oEAAK,WAAU;;wEAA2C;wEACvD,OAAO,QAAQ;wEAAC;;;;;;;;;;;;;sEAKxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,qNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAiB;wEACjC,eAAe,OAAO,cAAc;;;;;;;8EAE5C,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;wEAC7B,OAAO,OAAO,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAElC,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,OAAO,aAAa;wEAAC;;;;;;;gEAEvB,OAAO,SAAS,kBACf,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;wEACxB,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;sDAOtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,sBAAsB,EACrC,OAAO,WAAW,IAAI,IAClB,uCACA,kCACJ;;gEACC,OAAO,WAAW,IAAI,IAAI,MAAM;gEAAI,eAAe,OAAO,WAAW;;;;;;;sEAExE,6LAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;8DAK5D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS,IAAM,kBAAkB,OAAO,OAAO;wDAC/C,WAAW,CAAC,iCAAiC,EAC3C,eAAe,GAAG,CAAC,OAAO,OAAO,IAC7B,qEACA,0DACJ;wDACF,OAAO,eAAe,GAAG,CAAC,OAAO,OAAO,IAAI,kBAAkB;kEAE7D,eAAe,GAAG,CAAC,OAAO,OAAO,kBAChC,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;iFAEf,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA7EvB,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;AA0FnC;GA5XgB;KAAA"}}, {"offset": {"line": 2413, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2419, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/WalletAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  Legend\n} from 'recharts';\nimport { WalletData, WalletTrade, ScalpingMetrics } from '@/types/hyperliquid';\nimport {\n  ArrowLeft,\n  TrendingUp,\n  TrendingDown,\n  DollarSign,\n  Target,\n  Zap,\n  Activity,\n  BarChart3,\n  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,\n  Clock,\n  Brain,\n  AlertTriangle,\n  CheckCircle\n} from 'lucide-react';\n\ninterface WalletAnalysisProps {\n  wallet: WalletData;\n  trades: WalletTrade[];\n  scalpingMetrics: ScalpingMetrics;\n  onBack: () => void;\n}\n\nexport function WalletAnalysis({ wallet, trades, scalpingMetrics, onBack }: WalletAnalysisProps) {\n  const [activeChart, setActiveChart] = useState<'pnl' | 'volume' | 'trades' | 'symbols'>('pnl');\n\n  // Process data for charts\n  const chartData = useMemo(() => {\n    const sortedTrades = [...trades].sort((a, b) => a.timestamp - b.timestamp);\n    let cumulativePnl = 0;\n    let cumulativeVolume = 0;\n\n    const timeSeriesData = sortedTrades.map((trade, index) => {\n      cumulativePnl += trade.pnl || 0;\n      cumulativeVolume += trade.value;\n\n      return {\n        timestamp: trade.timestamp,\n        time: new Date(trade.timestamp).toLocaleTimeString(),\n        date: new Date(trade.timestamp).toLocaleDateString(),\n        cumulativePnl,\n        cumulativeVolume,\n        tradePnl: trade.pnl || 0,\n        tradeValue: trade.value,\n        tradeCount: index + 1,\n        symbol: trade.symbol,\n        side: trade.side\n      };\n    });\n\n    return timeSeriesData;\n  }, [trades]);\n\n  const symbolAnalysis = useMemo(() => {\n    const symbolMap = trades.reduce((acc, trade) => {\n      if (!acc[trade.symbol]) {\n        acc[trade.symbol] = {\n          symbol: trade.symbol,\n          totalTrades: 0,\n          totalVolume: 0,\n          totalPnl: 0,\n          winningTrades: 0,\n          losingTrades: 0,\n          avgTradeSize: 0,\n          winRate: 0,\n          profitFactor: 0\n        };\n      }\n\n      const symbolData = acc[trade.symbol];\n      symbolData.totalTrades++;\n      symbolData.totalVolume += trade.value;\n      symbolData.totalPnl += trade.pnl || 0;\n\n      if ((trade.pnl || 0) > 0) {\n        symbolData.winningTrades++;\n      } else if ((trade.pnl || 0) < 0) {\n        symbolData.losingTrades++;\n      }\n\n      return acc;\n    }, {} as Record<string, any>);\n\n    return Object.values(symbolMap).map((item: any) => ({\n      ...item,\n      avgTradeSize: item.totalVolume / item.totalTrades,\n      winRate: (item.winningTrades / item.totalTrades) * 100,\n      profitFactor: item.totalPnl > 0 ?\n        Math.abs(item.totalPnl) / Math.max(Math.abs(item.totalPnl - item.totalPnl), 1) : 0\n    })).sort((a: any, b: any) => b.totalVolume - a.totalVolume);\n  }, [trades]);\n\n  const tradingPatterns = useMemo(() => {\n    const hourlyActivity = new Array(24).fill(0);\n    const dailyActivity = new Array(7).fill(0);\n\n    trades.forEach(trade => {\n      const date = new Date(trade.timestamp);\n      const hour = date.getHours();\n      const day = date.getDay();\n\n      hourlyActivity[hour]++;\n      dailyActivity[day]++;\n    });\n\n    return {\n      hourlyActivity: hourlyActivity.map((count, hour) => ({\n        hour: `${hour}:00`,\n        trades: count,\n        percentage: (count / trades.length) * 100\n      })),\n      dailyActivity: dailyActivity.map((count, day) => ({\n        day: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][day],\n        trades: count,\n        percentage: (count / trades.length) * 100\n      }))\n    };\n  }, [trades]);\n\n  const strategyAnalysis = useMemo(() => {\n    const analysis = {\n      tradingStyle: 'Unknown',\n      riskProfile: 'Medium',\n      preferredSymbols: symbolAnalysis.slice(0, 3).map(s => s.symbol),\n      avgHoldTime: 0,\n      consistency: 0,\n      momentum: 'Neutral',\n      insights: [] as string[]\n    };\n\n    // Determine trading style\n    if (wallet.isScalper && scalpingMetrics.avgTimeBetweenTrades < 10) {\n      analysis.tradingStyle = 'High-Frequency Scalper';\n      analysis.insights.push('Executes rapid trades with minimal hold times');\n    } else if (wallet.avgTradeSize > 50000) {\n      analysis.tradingStyle = 'Whale Trader';\n      analysis.insights.push('Makes large position trades, likely institutional');\n    } else if (wallet.winRate > 70) {\n      analysis.tradingStyle = 'Precision Trader';\n      analysis.insights.push('High win rate suggests careful entry/exit timing');\n    } else {\n      analysis.tradingStyle = 'Active Trader';\n    }\n\n    // Risk profile\n    const maxTradeRatio = wallet.maxTradeSize / wallet.avgTradeSize;\n    if (maxTradeRatio > 5) {\n      analysis.riskProfile = 'High Risk';\n      analysis.insights.push('Significant variation in trade sizes indicates high risk tolerance');\n    } else if (wallet.winRate > 65) {\n      analysis.riskProfile = 'Conservative';\n      analysis.insights.push('Consistent win rate suggests risk-averse approach');\n    }\n\n    // Momentum\n    const recentTrades = trades.slice(-10);\n    const recentPnl = recentTrades.reduce((sum, trade) => sum + (trade.pnl || 0), 0);\n    if (recentPnl > 0) {\n      analysis.momentum = 'Bullish';\n      analysis.insights.push('Recent trades show positive momentum');\n    } else if (recentPnl < 0) {\n      analysis.momentum = 'Bearish';\n      analysis.insights.push('Recent performance shows negative trend');\n    }\n\n    // Consistency\n    const dailyPnls = chartData.reduce((acc, point, index) => {\n      const date = point.date;\n      if (!acc[date]) acc[date] = 0;\n      acc[date] += point.tradePnl;\n      return acc;\n    }, {} as Record<string, number>);\n\n    const pnlValues = Object.values(dailyPnls);\n    const positiveDays = pnlValues.filter(pnl => pnl > 0).length;\n    analysis.consistency = (positiveDays / pnlValues.length) * 100;\n\n    if (analysis.consistency > 70) {\n      analysis.insights.push('Highly consistent daily performance');\n    } else if (analysis.consistency < 40) {\n      analysis.insights.push('Volatile daily performance with mixed results');\n    }\n\n    return analysis;\n  }, [wallet, trades, scalpingMetrics, symbolAnalysis, chartData]);\n\n  const formatCurrency = (value: number) => {\n    if (Math.abs(value) >= 1000000) {\n      return `$${(value / 1000000).toFixed(2)}M`;\n    } else if (Math.abs(value) >= 1000) {\n      return `$${(value / 1000).toFixed(1)}K`;\n    } else {\n      return `$${value.toFixed(2)}`;\n    }\n  };\n\n  const formatAddress = (address: string) => {\n    return `${address.slice(0, 8)}...${address.slice(-6)}`;\n  };\n\n  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <button\n            onClick={onBack}\n            className=\"flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300\"\n          >\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back to Wallet List\n          </button>\n\n          <div className=\"flex items-center space-x-2\">\n            {wallet.isScalper && (\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\">\n                <Zap className=\"h-4 w-4 mr-1\" />\n                Scalper\n              </span>\n            )}\n            {wallet.tags.map(tag => (\n              <span key={tag} className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                {tag}\n              </span>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              {wallet.nickname || 'Wallet Analysis'}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 font-mono text-lg\">\n              {formatAddress(wallet.address)}\n            </p>\n          </div>\n\n          <div className=\"text-right\">\n            <div className={`text-3xl font-bold ${\n              wallet.totalPnl24h >= 0\n                ? 'text-green-600 dark:text-green-400'\n                : 'text-red-600 dark:text-red-400'\n            }`}>\n              {wallet.totalPnl24h >= 0 ? '+' : ''}{formatCurrency(wallet.totalPnl24h)}\n            </div>\n            <div className=\"text-gray-500 dark:text-gray-400\">24h PnL</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Key Metrics Grid */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <DollarSign className=\"h-6 w-6 text-blue-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {formatCurrency(wallet.totalVolume24h)}\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">24h Volume</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <Target className=\"h-6 w-6 text-green-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {wallet.winRate.toFixed(1)}%\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Win Rate</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <Activity className=\"h-6 w-6 text-purple-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {wallet.tradeCount24h}\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Trades</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <BarChart3 className=\"h-6 w-6 text-orange-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {formatCurrency(wallet.avgTradeSize)}\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Avg Trade</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <Clock className=\"h-6 w-6 text-indigo-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {scalpingMetrics.avgTimeBetweenTrades.toFixed(1)}m\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Avg Time</div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center\">\n          <Brain className=\"h-6 w-6 text-pink-500 mx-auto mb-2\" />\n          <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n            {strategyAnalysis.consistency.toFixed(0)}%\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">Consistency</div>\n        </div>\n      </div>\n\n      {/* Strategy Analysis */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center\">\n          <Brain className=\"h-5 w-5 mr-2 text-pink-500\" />\n          Strategy Analysis\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Trading Style</h4>\n            <p className=\"text-blue-600 dark:text-blue-400 font-medium\">{strategyAnalysis.tradingStyle}</p>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n              Risk Profile: {strategyAnalysis.riskProfile}\n            </p>\n          </div>\n\n          <div>\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Preferred Assets</h4>\n            <div className=\"flex flex-wrap gap-1\">\n              {strategyAnalysis.preferredSymbols.map(symbol => (\n                <span key={symbol} className=\"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-sm\">\n                  {symbol}\n                </span>\n              ))}\n            </div>\n          </div>\n\n          <div>\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Current Momentum</h4>\n            <div className={`flex items-center ${\n              strategyAnalysis.momentum === 'Bullish' ? 'text-green-600 dark:text-green-400' :\n              strategyAnalysis.momentum === 'Bearish' ? 'text-red-600 dark:text-red-400' :\n              'text-gray-600 dark:text-gray-400'\n            }`}>\n              {strategyAnalysis.momentum === 'Bullish' ? <TrendingUp className=\"h-4 w-4 mr-1\" /> :\n               strategyAnalysis.momentum === 'Bearish' ? <TrendingDown className=\"h-4 w-4 mr-1\" /> :\n               <Activity className=\"h-4 w-4 mr-1\" />}\n              {strategyAnalysis.momentum}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-4\">\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Key Insights</h4>\n          <ul className=\"space-y-1\">\n            {strategyAnalysis.insights.map((insight, index) => (\n              <li key={index} className=\"flex items-start text-sm text-gray-600 dark:text-gray-400\">\n                <CheckCircle className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                {insight}\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n\n      {/* Chart Navigation */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Performance Charts\n          </h3>\n\n          <div className=\"flex space-x-2\">\n            {[\n              { id: 'pnl', label: 'PnL Curve', icon: TrendingUp },\n              { id: 'volume', label: 'Volume', icon: BarChart3 },\n              { id: 'trades', label: 'Trade Activity', icon: Activity },\n              { id: 'symbols', label: 'Asset Distribution', icon: PieChartIcon }\n            ].map(({ id, label, icon: Icon }) => (\n              <button\n                key={id}\n                onClick={() => setActiveChart(id as any)}\n                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${\n                  activeChart === id\n                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'\n                    : 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200'\n                }`}\n              >\n                <Icon className=\"h-4 w-4 mr-2\" />\n                {label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"h-80\">\n          {activeChart === 'pnl' && (\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <AreaChart data={chartData}>\n                <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n                <XAxis dataKey=\"time\" tick={{ fontSize: 12 }} />\n                <YAxis tick={{ fontSize: 12 }} />\n                <Tooltip\n                  formatter={(value: number) => [formatCurrency(value), 'Cumulative PnL']}\n                  labelFormatter={(label) => `Time: ${label}`}\n                />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"cumulativePnl\"\n                  stroke=\"#3B82F6\"\n                  fill=\"#3B82F6\"\n                  fillOpacity={0.1}\n                />\n              </AreaChart>\n            </ResponsiveContainer>\n          )}\n\n          {activeChart === 'volume' && (\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <BarChart data={chartData.slice(-20)}>\n                <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n                <XAxis dataKey=\"time\" tick={{ fontSize: 12 }} />\n                <YAxis tick={{ fontSize: 12 }} />\n                <Tooltip\n                  formatter={(value: number) => [formatCurrency(value), 'Trade Value']}\n                />\n                <Bar dataKey=\"tradeValue\" fill=\"#10B981\" />\n              </BarChart>\n            </ResponsiveContainer>\n          )}\n\n          {activeChart === 'trades' && (\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <LineChart data={tradingPatterns.hourlyActivity}>\n                <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n                <XAxis dataKey=\"hour\" tick={{ fontSize: 12 }} />\n                <YAxis tick={{ fontSize: 12 }} />\n                <Tooltip />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"trades\"\n                  stroke=\"#F59E0B\"\n                  strokeWidth={2}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          )}\n\n          {activeChart === 'symbols' && (\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <PieChart>\n                <Pie\n                  data={symbolAnalysis}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  outerRadius={100}\n                  dataKey=\"totalVolume\"\n                  nameKey=\"symbol\"\n                  label={({ symbol, percent }) => `${symbol} ${(percent * 100).toFixed(0)}%`}\n                >\n                  {symbolAnalysis.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                  ))}\n                </Pie>\n                <Tooltip formatter={(value: number) => [formatCurrency(value), 'Volume']} />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          )}\n        </div>\n      </div>\n\n      {/* Detailed Trade History */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center\">\n          <Activity className=\"h-5 w-5 mr-2\" />\n          Complete Trade History ({trades.length} trades)\n        </h3>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full text-sm\">\n            <thead>\n              <tr className=\"border-b border-gray-200 dark:border-gray-700\">\n                <th className=\"text-left py-3 text-gray-500 dark:text-gray-400\">Time</th>\n                <th className=\"text-left py-3 text-gray-500 dark:text-gray-400\">Symbol</th>\n                <th className=\"text-left py-3 text-gray-500 dark:text-gray-400\">Side</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">Size</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">Price</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">Value</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">PnL</th>\n                <th className=\"text-right py-3 text-gray-500 dark:text-gray-400\">Fee</th>\n              </tr>\n            </thead>\n            <tbody>\n              {trades\n                .sort((a, b) => b.timestamp - a.timestamp)\n                .slice(0, 50)\n                .map((trade) => (\n                <tr key={trade.tradeId} className=\"border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"py-3 text-gray-600 dark:text-gray-400\">\n                    {new Date(trade.timestamp).toLocaleString()}\n                  </td>\n                  <td className=\"py-3 font-medium text-gray-900 dark:text-white\">\n                    {trade.symbol}\n                  </td>\n                  <td className=\"py-3\">\n                    <span className={`px-2 py-1 rounded text-xs font-medium ${\n                      trade.side === 'buy'\n                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n                    }`}>\n                      {trade.side.toUpperCase()}\n                    </span>\n                  </td>\n                  <td className=\"py-3 text-right font-mono text-gray-900 dark:text-white\">\n                    {trade.size.toFixed(4)}\n                  </td>\n                  <td className=\"py-3 text-right font-mono text-gray-900 dark:text-white\">\n                    ${trade.price.toFixed(2)}\n                  </td>\n                  <td className=\"py-3 text-right font-mono text-gray-900 dark:text-white\">\n                    {formatCurrency(trade.value)}\n                  </td>\n                  <td className={`py-3 text-right font-mono ${\n                    (trade.pnl || 0) >= 0\n                      ? 'text-green-600 dark:text-green-400'\n                      : 'text-red-600 dark:text-red-400'\n                  }`}>\n                    {(trade.pnl || 0) >= 0 ? '+' : ''}{formatCurrency(trade.pnl || 0)}\n                  </td>\n                  <td className=\"py-3 text-right font-mono text-gray-500 dark:text-gray-400\">\n                    {formatCurrency(trade.fee)}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {trades.length > 50 && (\n          <div className=\"mt-4 text-center\">\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              Showing latest 50 trades of {trades.length} total\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Symbol Performance Breakdown */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center\">\n          <BarChart3 className=\"h-5 w-5 mr-2\" />\n          Performance by Symbol\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {symbolAnalysis.map((symbol: any) => (\n            <div key={symbol.symbol} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">{symbol.symbol}</h4>\n                <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {symbol.totalTrades} trades\n                </span>\n              </div>\n\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Total Volume:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {formatCurrency(symbol.totalVolume)}\n                  </span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Total PnL:</span>\n                  <span className={`text-sm font-medium ${\n                    symbol.totalPnl >= 0\n                      ? 'text-green-600 dark:text-green-400'\n                      : 'text-red-600 dark:text-red-400'\n                  }`}>\n                    {symbol.totalPnl >= 0 ? '+' : ''}{formatCurrency(symbol.totalPnl)}\n                  </span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Win Rate:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {symbol.winRate.toFixed(1)}%\n                  </span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Avg Trade:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {formatCurrency(symbol.avgTradeSize)}\n                  </span>\n                </div>\n\n                {/* Win Rate Progress Bar */}\n                <div className=\"mt-3\">\n                  <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    <span>Win Rate</span>\n                    <span>{symbol.winRate.toFixed(1)}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div\n                      className={`h-2 rounded-full ${\n                        symbol.winRate >= 60 ? 'bg-green-500' :\n                        symbol.winRate >= 40 ? 'bg-yellow-500' : 'bg-red-500'\n                      }`}\n                      style={{ width: `${Math.min(symbol.winRate, 100)}%` }}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAmBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAlBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;AA4CO,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAuB;;IAC7F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2C;IAExF,0BAA0B;IAC1B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE;YACxB,MAAM,eAAe;mBAAI;aAAO,CAAC,IAAI;kEAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;;YACzE,IAAI,gBAAgB;YACpB,IAAI,mBAAmB;YAEvB,MAAM,iBAAiB,aAAa,GAAG;oEAAC,CAAC,OAAO;oBAC9C,iBAAiB,MAAM,GAAG,IAAI;oBAC9B,oBAAoB,MAAM,KAAK;oBAE/B,OAAO;wBACL,WAAW,MAAM,SAAS;wBAC1B,MAAM,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;wBAClD,MAAM,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;wBAClD;wBACA;wBACA,UAAU,MAAM,GAAG,IAAI;wBACvB,YAAY,MAAM,KAAK;wBACvB,YAAY,QAAQ;wBACpB,QAAQ,MAAM,MAAM;wBACpB,MAAM,MAAM,IAAI;oBAClB;gBACF;;YAEA,OAAO;QACT;4CAAG;QAAC;KAAO;IAEX,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YAC7B,MAAM,YAAY,OAAO,MAAM;oEAAC,CAAC,KAAK;oBACpC,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE;wBACtB,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG;4BAClB,QAAQ,MAAM,MAAM;4BACpB,aAAa;4BACb,aAAa;4BACb,UAAU;4BACV,eAAe;4BACf,cAAc;4BACd,cAAc;4BACd,SAAS;4BACT,cAAc;wBAChB;oBACF;oBAEA,MAAM,aAAa,GAAG,CAAC,MAAM,MAAM,CAAC;oBACpC,WAAW,WAAW;oBACtB,WAAW,WAAW,IAAI,MAAM,KAAK;oBACrC,WAAW,QAAQ,IAAI,MAAM,GAAG,IAAI;oBAEpC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG;wBACxB,WAAW,aAAa;oBAC1B,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG;wBAC/B,WAAW,YAAY;oBACzB;oBAEA,OAAO;gBACT;mEAAG,CAAC;YAEJ,OAAO,OAAO,MAAM,CAAC,WAAW,GAAG;0DAAC,CAAC,OAAc,CAAC;wBAClD,GAAG,IAAI;wBACP,cAAc,KAAK,WAAW,GAAG,KAAK,WAAW;wBACjD,SAAS,AAAC,KAAK,aAAa,GAAG,KAAK,WAAW,GAAI;wBACnD,cAAc,KAAK,QAAQ,GAAG,IAC5B,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK;oBACrF,CAAC;yDAAG,IAAI;0DAAC,CAAC,GAAQ,IAAW,EAAE,WAAW,GAAG,EAAE,WAAW;;QAC5D;iDAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YAC9B,MAAM,iBAAiB,IAAI,MAAM,IAAI,IAAI,CAAC;YAC1C,MAAM,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;YAExC,OAAO,OAAO;2DAAC,CAAA;oBACb,MAAM,OAAO,IAAI,KAAK,MAAM,SAAS;oBACrC,MAAM,OAAO,KAAK,QAAQ;oBAC1B,MAAM,MAAM,KAAK,MAAM;oBAEvB,cAAc,CAAC,KAAK;oBACpB,aAAa,CAAC,IAAI;gBACpB;;YAEA,OAAO;gBACL,gBAAgB,eAAe,GAAG;+DAAC,CAAC,OAAO,OAAS,CAAC;4BACnD,MAAM,GAAG,KAAK,GAAG,CAAC;4BAClB,QAAQ;4BACR,YAAY,AAAC,QAAQ,OAAO,MAAM,GAAI;wBACxC,CAAC;;gBACD,eAAe,cAAc,GAAG;+DAAC,CAAC,OAAO,MAAQ,CAAC;4BAChD,KAAK;gCAAC;gCAAO;gCAAO;gCAAO;gCAAO;gCAAO;gCAAO;6BAAM,CAAC,IAAI;4BAC3D,QAAQ;4BACR,YAAY,AAAC,QAAQ,OAAO,MAAM,GAAI;wBACxC,CAAC;;YACH;QACF;kDAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;YAC/B,MAAM,WAAW;gBACf,cAAc;gBACd,aAAa;gBACb,kBAAkB,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG;gEAAC,CAAA,IAAK,EAAE,MAAM;;gBAC9D,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,UAAU,EAAE;YACd;YAEA,0BAA0B;YAC1B,IAAI,OAAO,SAAS,IAAI,gBAAgB,oBAAoB,GAAG,IAAI;gBACjE,SAAS,YAAY,GAAG;gBACxB,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB,OAAO,IAAI,OAAO,YAAY,GAAG,OAAO;gBACtC,SAAS,YAAY,GAAG;gBACxB,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB,OAAO,IAAI,OAAO,OAAO,GAAG,IAAI;gBAC9B,SAAS,YAAY,GAAG;gBACxB,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB,OAAO;gBACL,SAAS,YAAY,GAAG;YAC1B;YAEA,eAAe;YACf,MAAM,gBAAgB,OAAO,YAAY,GAAG,OAAO,YAAY;YAC/D,IAAI,gBAAgB,GAAG;gBACrB,SAAS,WAAW,GAAG;gBACvB,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB,OAAO,IAAI,OAAO,OAAO,GAAG,IAAI;gBAC9B,SAAS,WAAW,GAAG;gBACvB,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB;YAEA,WAAW;YACX,MAAM,eAAe,OAAO,KAAK,CAAC,CAAC;YACnC,MAAM,YAAY,aAAa,MAAM;sEAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;qEAAG;YAC9E,IAAI,YAAY,GAAG;gBACjB,SAAS,QAAQ,GAAG;gBACpB,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB,OAAO,IAAI,YAAY,GAAG;gBACxB,SAAS,QAAQ,GAAG;gBACpB,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB;YAEA,cAAc;YACd,MAAM,YAAY,UAAU,MAAM;sEAAC,CAAC,KAAK,OAAO;oBAC9C,MAAM,OAAO,MAAM,IAAI;oBACvB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG;oBAC5B,GAAG,CAAC,KAAK,IAAI,MAAM,QAAQ;oBAC3B,OAAO;gBACT;qEAAG,CAAC;YAEJ,MAAM,YAAY,OAAO,MAAM,CAAC;YAChC,MAAM,eAAe,UAAU,MAAM;4DAAC,CAAA,MAAO,MAAM;2DAAG,MAAM;YAC5D,SAAS,WAAW,GAAG,AAAC,eAAe,UAAU,MAAM,GAAI;YAE3D,IAAI,SAAS,WAAW,GAAG,IAAI;gBAC7B,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB,OAAO,IAAI,SAAS,WAAW,GAAG,IAAI;gBACpC,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB;YAEA,OAAO;QACT;mDAAG;QAAC;QAAQ;QAAQ;QAAiB;QAAgB;KAAU;IAE/D,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,GAAG,CAAC,UAAU,SAAS;YAC9B,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO,IAAI,KAAK,GAAG,CAAC,UAAU,MAAM;YAClC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO;YACL,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;QAC/B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,IAAI;IACxD;IAEA,MAAM,SAAS;QAAC;QAAW;QAAW;QAAW;QAAW;QAAW;KAAU;IAEjF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIxC,6LAAC;gCAAI,WAAU;;oCACZ,OAAO,SAAS,kBACf,6LAAC;wCAAK,WAAU;;0DACd,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAInC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA,oBACf,6LAAC;4CAAe,WAAU;sDACvB;2CADQ;;;;;;;;;;;;;;;;;kCAOjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,OAAO,QAAQ,IAAI;;;;;;kDAEtB,6LAAC;wCAAE,WAAU;kDACV,cAAc,OAAO,OAAO;;;;;;;;;;;;0CAIjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,mBAAmB,EAClC,OAAO,WAAW,IAAI,IAClB,uCACA,kCACJ;;4CACC,OAAO,WAAW,IAAI,IAAI,MAAM;4CAAI,eAAe,OAAO,WAAW;;;;;;;kDAExE,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;0BAMxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;gCAAI,WAAU;0CACZ,eAAe,OAAO,cAAc;;;;;;0CAEvC,6LAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAI,WAAU;;oCACZ,OAAO,OAAO,CAAC,OAAO,CAAC;oCAAG;;;;;;;0CAE7B,6LAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAI,WAAU;0CACZ,OAAO,aAAa;;;;;;0CAEvB,6LAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAI,WAAU;0CACZ,eAAe,OAAO,YAAY;;;;;;0CAErC,6LAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAI,WAAU;;oCACZ,gBAAgB,oBAAoB,CAAC,OAAO,CAAC;oCAAG;;;;;;;0CAEnD,6LAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAI,WAAU;;oCACZ,iBAAiB,WAAW,CAAC,OAAO,CAAC;oCAAG;;;;;;;0CAE3C,6LAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;0BAK9D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAA+B;;;;;;;kCAIlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAC/D,6LAAC;wCAAE,WAAU;kDAAgD,iBAAiB,YAAY;;;;;;kDAC1F,6LAAC;wCAAE,WAAU;;4CAAgD;4CAC5C,iBAAiB,WAAW;;;;;;;;;;;;;0CAI/C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAC/D,6LAAC;wCAAI,WAAU;kDACZ,iBAAiB,gBAAgB,CAAC,GAAG,CAAC,CAAA,uBACrC,6LAAC;gDAAkB,WAAU;0DAC1B;+CADQ;;;;;;;;;;;;;;;;0CAOjB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAC/D,6LAAC;wCAAI,WAAW,CAAC,kBAAkB,EACjC,iBAAiB,QAAQ,KAAK,YAAY,uCAC1C,iBAAiB,QAAQ,KAAK,YAAY,mCAC1C,oCACA;;4CACC,iBAAiB,QAAQ,KAAK,0BAAY,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;uDAChE,iBAAiB,QAAQ,KAAK,0BAAY,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;qEAClE,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACpB,iBAAiB,QAAQ;;;;;;;;;;;;;;;;;;;kCAKhC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAC/D,6LAAC;gCAAG,WAAU;0CACX,iBAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACvC,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB;;uCAFM;;;;;;;;;;;;;;;;;;;;;;0BAUjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAIpE,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,IAAI;wCAAO,OAAO;wCAAa,MAAM,qNAAA,CAAA,aAAU;oCAAC;oCAClD;wCAAE,IAAI;wCAAU,OAAO;wCAAU,MAAM,qNAAA,CAAA,YAAS;oCAAC;oCACjD;wCAAE,IAAI;wCAAU,OAAO;wCAAkB,MAAM,6MAAA,CAAA,WAAQ;oCAAC;oCACxD;wCAAE,IAAI;wCAAW,OAAO;wCAAsB,MAAM,iNAAA,CAAA,WAAY;oCAAC;iCAClE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC9B,6LAAC;wCAEC,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,6EAA6E,EACvF,gBAAgB,KACZ,kEACA,iFACJ;;0DAEF,6LAAC;gDAAK,WAAU;;;;;;4CACf;;uCATI;;;;;;;;;;;;;;;;kCAeb,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,uBACf,6LAAC,sKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,6LAAC,gKAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,WAAU;;;;;;sDAC/C,6LAAC,wJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC3C,6LAAC,wJAAA,CAAA,QAAK;4CAAC,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC5B,6LAAC,0JAAA,CAAA,UAAO;4CACN,WAAW,CAAC,QAAkB;oDAAC,eAAe;oDAAQ;iDAAiB;4CACvE,gBAAgB,CAAC,QAAU,CAAC,MAAM,EAAE,OAAO;;;;;;sDAE7C,6LAAC,uJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,MAAK;4CACL,aAAa;;;;;;;;;;;;;;;;;4BAMpB,gBAAgB,0BACf,6LAAC,sKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;oCAAC,MAAM,UAAU,KAAK,CAAC,CAAC;;sDAC/B,6LAAC,gKAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,WAAU;;;;;;sDAC/C,6LAAC,wJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC3C,6LAAC,wJAAA,CAAA,QAAK;4CAAC,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC5B,6LAAC,0JAAA,CAAA,UAAO;4CACN,WAAW,CAAC,QAAkB;oDAAC,eAAe;oDAAQ;iDAAc;;;;;;sDAEtE,6LAAC,sJAAA,CAAA,MAAG;4CAAC,SAAQ;4CAAa,MAAK;;;;;;;;;;;;;;;;;4BAKpC,gBAAgB,0BACf,6LAAC,sKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;oCAAC,MAAM,gBAAgB,cAAc;;sDAC7C,6LAAC,gKAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,WAAU;;;;;;sDAC/C,6LAAC,wJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC3C,6LAAC,wJAAA,CAAA,QAAK;4CAAC,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAC5B,6LAAC,0JAAA,CAAA,UAAO;;;;;sDACR,6LAAC,uJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,aAAa;;;;;;;;;;;;;;;;;4BAMpB,gBAAgB,2BACf,6LAAC,sKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;sDACP,6LAAC,kJAAA,CAAA,MAAG;4CACF,MAAM;4CACN,IAAG;4CACH,IAAG;4CACH,aAAa;4CACb,SAAQ;4CACR,SAAQ;4CACR,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAK,GAAG,OAAO,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;sDAEzE,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC,uJAAA,CAAA,OAAI;oDAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;mDAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;sDAG9B,6LAAC,0JAAA,CAAA,UAAO;4CAAC,WAAW,CAAC,QAAkB;oDAAC,eAAe;oDAAQ;iDAAS;;;;;;sDACxE,6LAAC,yJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;4BACZ,OAAO,MAAM;4BAAC;;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;8CACC,cAAA,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;;;;;;;;;;;;8CAGrE,6LAAC;8CACE,OACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,sBACN,6LAAC;4CAAuB,WAAU;;8DAChC,6LAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc;;;;;;8DAE3C,6LAAC;oDAAG,WAAU;8DACX,MAAM,MAAM;;;;;;8DAEf,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,CAAC,sCAAsC,EACtD,MAAM,IAAI,KAAK,QACX,sEACA,6DACJ;kEACC,MAAM,IAAI,CAAC,WAAW;;;;;;;;;;;8DAG3B,6LAAC;oDAAG,WAAU;8DACX,MAAM,IAAI,CAAC,OAAO,CAAC;;;;;;8DAEtB,6LAAC;oDAAG,WAAU;;wDAA0D;wDACpE,MAAM,KAAK,CAAC,OAAO,CAAC;;;;;;;8DAExB,6LAAC;oDAAG,WAAU;8DACX,eAAe,MAAM,KAAK;;;;;;8DAE7B,6LAAC;oDAAG,WAAW,CAAC,0BAA0B,EACxC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAChB,uCACA,kCACJ;;wDACC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,MAAM;wDAAI,eAAe,MAAM,GAAG,IAAI;;;;;;;8DAEjE,6LAAC;oDAAG,WAAU;8DACX,eAAe,MAAM,GAAG;;;;;;;2CAjCpB,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;oBAyC7B,OAAO,MAAM,GAAG,oBACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAA2C;gCACzB,OAAO,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAOnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,qNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;gCAAwB,WAAU;;kDACjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6C,OAAO,MAAM;;;;;;0DACxE,6LAAC;gDAAK,WAAU;;oDACb,OAAO,WAAW;oDAAC;;;;;;;;;;;;;kDAIxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAU;kEACb,eAAe,OAAO,WAAW;;;;;;;;;;;;0DAItC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,QAAQ,IAAI,IACf,uCACA,kCACJ;;4DACC,OAAO,QAAQ,IAAI,IAAI,MAAM;4DAAI,eAAe,OAAO,QAAQ;;;;;;;;;;;;;0DAIpE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAU;;4DACb,OAAO,OAAO,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAI/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAU;kEACb,eAAe,OAAO,YAAY;;;;;;;;;;;;0DAKvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM,OAAO,OAAO,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEnC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAW,CAAC,iBAAiB,EAC3B,OAAO,OAAO,IAAI,KAAK,iBACvB,OAAO,OAAO,IAAI,KAAK,kBAAkB,cACzC;4DACF,OAAO;gEAAE,OAAO,GAAG,KAAK,GAAG,CAAC,OAAO,OAAO,EAAE,KAAK,CAAC,CAAC;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;+BArDpD,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;AAgEnC;GAhlBgB;KAAA"}}, {"offset": {"line": 4007, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4013, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/components/LiveTradingFeed.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { WalletData, WalletTrade, LiveTradingActivity, RealTimeUpdate } from '@/types/hyperliquid';\nimport { \n  Activity, \n  Zap, \n  TrendingUp, \n  TrendingDown, \n  Clock, \n  DollarSign,\n  Target,\n  AlertTriangle,\n  Flame,\n  Eye,\n  Radio\n} from 'lucide-react';\n\ninterface LiveTradingFeedProps {\n  wallets: WalletData[];\n  liveActivity: LiveTradingActivity[];\n  recentTrades: WalletTrade[];\n  onWalletClick: (wallet: WalletData) => void;\n}\n\nexport function LiveTradingFeed({ \n  wallets, \n  liveActivity, \n  recentTrades, \n  onWalletClick \n}: LiveTradingFeedProps) {\n  const [updates, setUpdates] = useState<RealTimeUpdate[]>([]);\n  const [filter, setFilter] = useState<'all' | 'active' | 'hot' | 'high_volume'>('all');\n\n  // Simulate real-time updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      // Generate random update\n      const randomWallet = wallets[Math.floor(Math.random() * wallets.length)];\n      const updateTypes = ['new_trade', 'position_change', 'pnl_update', 'activity_spike'] as const;\n      const priorities = ['low', 'medium', 'high', 'urgent'] as const;\n      \n      const newUpdate: RealTimeUpdate = {\n        type: updateTypes[Math.floor(Math.random() * updateTypes.length)],\n        walletAddress: randomWallet.address,\n        timestamp: Date.now(),\n        data: {\n          symbol: ['HYPER', 'BTC', 'ETH'][Math.floor(Math.random() * 3)],\n          value: Math.random() * 50000 + 1000,\n          side: Math.random() > 0.5 ? 'buy' : 'sell'\n        },\n        priority: priorities[Math.floor(Math.random() * priorities.length)]\n      };\n\n      setUpdates(prev => [newUpdate, ...prev.slice(0, 49)]); // Keep last 50 updates\n    }, 2000 + Math.random() * 3000); // Random interval 2-5 seconds\n\n    return () => clearInterval(interval);\n  }, [wallets]);\n\n  const filteredActivity = liveActivity.filter(activity => {\n    switch (filter) {\n      case 'active':\n        return activity.isActiveNow;\n      case 'hot':\n        return activity.hotScore > 70;\n      case 'high_volume':\n        return activity.volumeInLastHour > 100000;\n      default:\n        return true;\n    }\n  }).sort((a, b) => b.hotScore - a.hotScore);\n\n  const formatCurrency = (value: number) => {\n    if (Math.abs(value) >= 1000000) {\n      return `$${(value / 1000000).toFixed(2)}M`;\n    } else if (Math.abs(value) >= 1000) {\n      return `$${(value / 1000).toFixed(1)}K`;\n    } else {\n      return `$${value.toFixed(2)}`;\n    }\n  };\n\n  const formatAddress = (address: string) => {\n    return `${address.slice(0, 6)}...${address.slice(-4)}`;\n  };\n\n  const formatTimeAgo = (timestamp: number) => {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const seconds = Math.floor(diff / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n\n    if (hours > 0) return `${hours}h ago`;\n    if (minutes > 0) return `${minutes}m ago`;\n    return `${seconds}s ago`;\n  };\n\n  const getWalletByAddress = (address: string) => {\n    return wallets.find(w => w.address === address);\n  };\n\n  const getHotScoreColor = (score: number) => {\n    if (score >= 80) return 'text-red-500';\n    if (score >= 60) return 'text-orange-500';\n    if (score >= 40) return 'text-yellow-500';\n    return 'text-gray-500';\n  };\n\n  const getUpdateIcon = (type: RealTimeUpdate['type']) => {\n    switch (type) {\n      case 'new_trade': return <Activity className=\"h-4 w-4\" />;\n      case 'position_change': return <TrendingUp className=\"h-4 w-4\" />;\n      case 'pnl_update': return <DollarSign className=\"h-4 w-4\" />;\n      case 'activity_spike': return <Zap className=\"h-4 w-4\" />;\n      default: return <Radio className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getPriorityColor = (priority: RealTimeUpdate['priority']) => {\n    switch (priority) {\n      case 'urgent': return 'text-red-600 bg-red-50 dark:bg-red-900/20';\n      case 'high': return 'text-orange-600 bg-orange-50 dark:bg-orange-900/20';\n      case 'medium': return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20';\n      default: return 'text-gray-600 bg-gray-50 dark:bg-gray-700';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n            <Radio className=\"h-6 w-6 mr-2 text-red-500 animate-pulse\" />\n            Live Trading Feed\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Real-time wallet activity and trading updates\n          </p>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <div className=\"flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/20 rounded-full\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2\"></div>\n            <span className=\"text-sm text-green-700 dark:text-green-300\">Live</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Filter Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n        <div className=\"flex space-x-4\">\n          {[\n            { id: 'all', label: 'All Wallets', count: liveActivity.length },\n            { id: 'active', label: 'Active Now', count: liveActivity.filter(a => a.isActiveNow).length },\n            { id: 'hot', label: 'Hot Wallets', count: liveActivity.filter(a => a.hotScore > 70).length },\n            { id: 'high_volume', label: 'High Volume', count: liveActivity.filter(a => a.volumeInLastHour > 100000).length }\n          ].map(({ id, label, count }) => (\n            <button\n              key={id}\n              onClick={() => setFilter(id as any)}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                filter === id\n                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'\n                  : 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200'\n              }`}\n            >\n              {label} ({count})\n            </button>\n          ))}\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Active Wallets */}\n        <div className=\"lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n              <Flame className=\"h-5 w-5 mr-2 text-orange-500\" />\n              Active Wallets ({filteredActivity.length})\n            </h3>\n          </div>\n          \n          <div className=\"divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto\">\n            {filteredActivity.map((activity) => {\n              const wallet = getWalletByAddress(activity.walletAddress);\n              if (!wallet) return null;\n\n              return (\n                <div \n                  key={activity.walletAddress} \n                  className=\"px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors\"\n                  onClick={() => onWalletClick(wallet)}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"flex items-center space-x-2\">\n                        {activity.isActiveNow && (\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                        )}\n                        <span className=\"font-mono text-sm text-gray-900 dark:text-white\">\n                          {formatAddress(activity.walletAddress)}\n                        </span>\n                        {wallet.nickname && (\n                          <span className=\"text-sm text-blue-600 dark:text-blue-400\">\n                            ({wallet.nickname})\n                          </span>\n                        )}\n                      </div>\n                      \n                      <div className=\"flex items-center space-x-2\">\n                        {wallet.isScalper && (\n                          <Zap className=\"h-4 w-4 text-yellow-500\" title=\"Scalper\" />\n                        )}\n                        <span className={`text-sm font-medium ${getHotScoreColor(activity.hotScore)}`}>\n                          🔥 {activity.hotScore}\n                        </span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"text-right\">\n                      <div className=\"flex items-center space-x-4 text-sm\">\n                        <div className=\"text-center\">\n                          <div className=\"font-medium text-gray-900 dark:text-white\">\n                            {activity.tradesInLast5Min}\n                          </div>\n                          <div className=\"text-xs text-gray-500 dark:text-gray-400\">5m trades</div>\n                        </div>\n                        \n                        <div className=\"text-center\">\n                          <div className=\"font-medium text-gray-900 dark:text-white\">\n                            {formatCurrency(activity.volumeInLastHour)}\n                          </div>\n                          <div className=\"text-xs text-gray-500 dark:text-gray-400\">1h volume</div>\n                        </div>\n                        \n                        <div className=\"text-center\">\n                          <div className={`font-medium ${\n                            activity.pnlInLastHour >= 0 \n                              ? 'text-green-600 dark:text-green-400' \n                              : 'text-red-600 dark:text-red-400'\n                          }`}>\n                            {activity.pnlInLastHour >= 0 ? '+' : ''}{formatCurrency(activity.pnlInLastHour)}\n                          </div>\n                          <div className=\"text-xs text-gray-500 dark:text-gray-400\">1h PnL</div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex items-center justify-end space-x-2 mt-2\">\n                        <span className={`px-2 py-1 rounded text-xs font-medium ${\n                          activity.momentum === 'bullish' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                          activity.momentum === 'bearish' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :\n                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n                        }`}>\n                          {activity.momentum}\n                        </span>\n                        \n                        <span className={`px-2 py-1 rounded text-xs font-medium ${\n                          activity.riskLevel === 'extreme' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :\n                          activity.riskLevel === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :\n                          activity.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :\n                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n                        }`}>\n                          {activity.riskLevel} risk\n                        </span>\n                        \n                        {activity.currentStreak > 0 && (\n                          <span className={`px-2 py-1 rounded text-xs font-medium ${\n                            activity.streakType === 'winning' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                            'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n                          }`}>\n                            {activity.currentStreak} {activity.streakType}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Real-time Updates Feed */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n              <Activity className=\"h-5 w-5 mr-2 text-blue-500\" />\n              Live Updates\n            </h3>\n          </div>\n          \n          <div className=\"divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto\">\n            {updates.slice(0, 20).map((update, index) => {\n              const wallet = getWalletByAddress(update.walletAddress);\n              \n              return (\n                <div key={`${update.timestamp}-${index}`} className={`px-4 py-3 ${getPriorityColor(update.priority)}`}>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"flex-shrink-0 mt-1\">\n                      {getUpdateIcon(update.type)}\n                    </div>\n                    \n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center justify-between\">\n                        <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {formatAddress(update.walletAddress)}\n                          {wallet?.nickname && (\n                            <span className=\"text-xs text-gray-500 ml-1\">\n                              ({wallet.nickname})\n                            </span>\n                          )}\n                        </p>\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                          {formatTimeAgo(update.timestamp)}\n                        </span>\n                      </div>\n                      \n                      <p className=\"text-xs text-gray-600 dark:text-gray-400 mt-1\">\n                        {update.type === 'new_trade' && `${update.data.side.toUpperCase()} ${update.data.symbol} - ${formatCurrency(update.data.value)}`}\n                        {update.type === 'position_change' && `Position updated for ${update.data.symbol}`}\n                        {update.type === 'pnl_update' && `PnL changed: ${formatCurrency(update.data.value)}`}\n                        {update.type === 'activity_spike' && `High activity detected - ${update.data.symbol}`}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;AAyBO,SAAS,gBAAgB,EAC9B,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,aAAa,EACQ;;IACrB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IAE/E,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,WAAW;sDAAY;oBAC3B,yBAAyB;oBACzB,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;oBACxE,MAAM,cAAc;wBAAC;wBAAa;wBAAmB;wBAAc;qBAAiB;oBACpF,MAAM,aAAa;wBAAC;wBAAO;wBAAU;wBAAQ;qBAAS;oBAEtD,MAAM,YAA4B;wBAChC,MAAM,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;wBACjE,eAAe,aAAa,OAAO;wBACnC,WAAW,KAAK,GAAG;wBACnB,MAAM;4BACJ,QAAQ;gCAAC;gCAAS;gCAAO;6BAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;4BAC9D,OAAO,KAAK,MAAM,KAAK,QAAQ;4BAC/B,MAAM,KAAK,MAAM,KAAK,MAAM,QAAQ;wBACtC;wBACA,UAAU,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;oBACrE;oBAEA;8DAAW,CAAA,OAAQ;gCAAC;mCAAc,KAAK,KAAK,CAAC,GAAG;6BAAI;8DAAG,uBAAuB;gBAChF;qDAAG,OAAO,KAAK,MAAM,KAAK,OAAO,8BAA8B;YAE/D;6CAAO,IAAM,cAAc;;QAC7B;oCAAG;QAAC;KAAQ;IAEZ,MAAM,mBAAmB,aAAa,MAAM,CAAC,CAAA;QAC3C,OAAQ;YACN,KAAK;gBACH,OAAO,SAAS,WAAW;YAC7B,KAAK;gBACH,OAAO,SAAS,QAAQ,GAAG;YAC7B,KAAK;gBACH,OAAO,SAAS,gBAAgB,GAAG;YACrC;gBACE,OAAO;QACX;IACF,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAEzC,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,GAAG,CAAC,UAAU,SAAS;YAC9B,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO,IAAI,KAAK,GAAG,CAAC,UAAU,MAAM;YAClC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO;YACL,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;QAC/B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,IAAI;IACxD;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAO,MAAM;QACnB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QAEnC,IAAI,QAAQ,GAAG,OAAO,GAAG,MAAM,KAAK,CAAC;QACrC,IAAI,UAAU,GAAG,OAAO,GAAG,QAAQ,KAAK,CAAC;QACzC,OAAO,GAAG,QAAQ,KAAK,CAAC;IAC1B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;IACzC;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAmB,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YACrD,KAAK;gBAAc,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAkB,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YAC7C;gBAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAA4C;;;;;;;0CAG/D,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAA6C;;;;;;;;;;;;;;;;;;;;;;;0BAMnE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,IAAI;4BAAO,OAAO;4BAAe,OAAO,aAAa,MAAM;wBAAC;wBAC9D;4BAAE,IAAI;4BAAU,OAAO;4BAAc,OAAO,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;wBAAC;wBAC3F;4BAAE,IAAI;4BAAO,OAAO;4BAAe,OAAO,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,IAAI,MAAM;wBAAC;wBAC3F;4BAAE,IAAI;4BAAe,OAAO;4BAAe,OAAO,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,GAAG,QAAQ,MAAM;wBAAC;qBAChH,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBACzB,6LAAC;4BAEC,SAAS,IAAM,UAAU;4BACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,KACP,kEACA,iFACJ;;gCAED;gCAAM;gCAAG;gCAAM;;2BARX;;;;;;;;;;;;;;;0BAcb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiC;wCACjC,iBAAiB,MAAM;wCAAC;;;;;;;;;;;;0CAI7C,6LAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC;oCACrB,MAAM,SAAS,mBAAmB,SAAS,aAAa;oCACxD,IAAI,CAAC,QAAQ,OAAO;oCAEpB,qBACE,6LAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,cAAc;kDAE7B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,SAAS,WAAW,kBACnB,6LAAC;oEAAI,WAAU;;;;;;8EAEjB,6LAAC;oEAAK,WAAU;8EACb,cAAc,SAAS,aAAa;;;;;;gEAEtC,OAAO,QAAQ,kBACd,6LAAC;oEAAK,WAAU;;wEAA2C;wEACvD,OAAO,QAAQ;wEAAC;;;;;;;;;;;;;sEAKxB,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,SAAS,kBACf,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;oEAA0B,OAAM;;;;;;8EAEjD,6LAAC;oEAAK,WAAW,CAAC,oBAAoB,EAAE,iBAAiB,SAAS,QAAQ,GAAG;;wEAAE;wEACzE,SAAS,QAAQ;;;;;;;;;;;;;;;;;;;8DAK3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACZ,SAAS,gBAAgB;;;;;;sFAE5B,6LAAC;4EAAI,WAAU;sFAA2C;;;;;;;;;;;;8EAG5D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACZ,eAAe,SAAS,gBAAgB;;;;;;sFAE3C,6LAAC;4EAAI,WAAU;sFAA2C;;;;;;;;;;;;8EAG5D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAW,CAAC,YAAY,EAC3B,SAAS,aAAa,IAAI,IACtB,uCACA,kCACJ;;gFACC,SAAS,aAAa,IAAI,IAAI,MAAM;gFAAI,eAAe,SAAS,aAAa;;;;;;;sFAEhF,6LAAC;4EAAI,WAAU;sFAA2C;;;;;;;;;;;;;;;;;;sEAI9D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAC,sCAAsC,EACtD,SAAS,QAAQ,KAAK,YAAY,sEAClC,SAAS,QAAQ,KAAK,YAAY,8DAClC,iEACA;8EACC,SAAS,QAAQ;;;;;;8EAGpB,6LAAC;oEAAK,WAAW,CAAC,sCAAsC,EACtD,SAAS,SAAS,KAAK,YAAY,8DACnC,SAAS,SAAS,KAAK,SAAS,0EAChC,SAAS,SAAS,KAAK,WAAW,0EAClC,qEACA;;wEACC,SAAS,SAAS;wEAAC;;;;;;;gEAGrB,SAAS,aAAa,GAAG,mBACxB,6LAAC;oEAAK,WAAW,CAAC,sCAAsC,EACtD,SAAS,UAAU,KAAK,YAAY,sEACpC,6DACA;;wEACC,SAAS,aAAa;wEAAC;wEAAE,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;uCAjFlD,SAAS,aAAa;;;;;gCAyFjC;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA+B;;;;;;;;;;;;0CAKvD,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ;oCACjC,MAAM,SAAS,mBAAmB,OAAO,aAAa;oCAEtD,qBACE,6LAAC;wCAAyC,WAAW,CAAC,UAAU,EAAE,iBAAiB,OAAO,QAAQ,GAAG;kDACnG,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,cAAc,OAAO,IAAI;;;;;;8DAG5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,cAAc,OAAO,aAAa;wEAClC,QAAQ,0BACP,6LAAC;4EAAK,WAAU;;gFAA6B;gFACzC,OAAO,QAAQ;gFAAC;;;;;;;;;;;;;8EAIxB,6LAAC;oEAAK,WAAU;8EACb,cAAc,OAAO,SAAS;;;;;;;;;;;;sEAInC,6LAAC;4DAAE,WAAU;;gEACV,OAAO,IAAI,KAAK,eAAe,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,eAAe,OAAO,IAAI,CAAC,KAAK,GAAG;gEAC/H,OAAO,IAAI,KAAK,qBAAqB,CAAC,qBAAqB,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE;gEACjF,OAAO,IAAI,KAAK,gBAAgB,CAAC,aAAa,EAAE,eAAe,OAAO,IAAI,CAAC,KAAK,GAAG;gEACnF,OAAO,IAAI,KAAK,oBAAoB,CAAC,yBAAyB,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;uCAzBnF,GAAG,OAAO,SAAS,CAAC,CAAC,EAAE,OAAO;;;;;gCA+B5C;;;;;;;;;;;;;;;;;;;;;;;;AAMZ;GAvTgB;KAAA"}}, {"offset": {"line": 4729, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4735, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/hooks/useHyperliquidWebSocket.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport {\n  HyperliquidSubscription,\n  PriceData,\n  OrderBookData,\n  TradeData,\n  CandleData,\n  ConnectionStatus,\n  SubscriptionState,\n  HyperliquidWebSocketResponse,\n  OrderBookLevel\n} from '@/types/hyperliquid';\n\nconst HYPERLIQUID_WS_URL = 'wss://api.hyperliquid.xyz/ws';\n\nexport function useHyperliquidWebSocket() {\n  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');\n  const [subscriptions, setSubscriptions] = useState<SubscriptionState[]>([]);\n  const [priceData, setPriceData] = useState<PriceData[]>([\n    // Mock data for demonstration\n    {\n      symbol: 'HYPER',\n      price: 28.50,\n      timestamp: Date.now(),\n      volume: 2456789,\n      change24h: 5.8,\n      high24h: 29.20,\n      low24h: 27.10\n    },\n    {\n      symbol: 'BTC',\n      price: 45000,\n      timestamp: Date.now(),\n      volume: 1234567,\n      change24h: 2.5,\n      high24h: 46000,\n      low24h: 44000\n    },\n    {\n      symbol: 'ETH',\n      price: 3200,\n      timestamp: Date.now(),\n      volume: 987654,\n      change24h: -1.2,\n      high24h: 3300,\n      low24h: 3100\n    }\n  ]);\n  const [orderBookData, setOrderBookData] = useState<OrderBookData | null>({\n    symbol: 'HYPER',\n    bids: [\n      { price: 28.45, size: 150.5 },\n      { price: 28.40, size: 220.2 },\n      { price: 28.35, size: 180.8 },\n      { price: 28.30, size: 310.1 },\n      { price: 28.25, size: 275.5 }\n    ],\n    asks: [\n      { price: 28.55, size: 125.7 },\n      { price: 28.60, size: 190.1 },\n      { price: 28.65, size: 165.9 },\n      { price: 28.70, size: 240.8 },\n      { price: 28.75, size: 200.3 }\n    ],\n    timestamp: Date.now()\n  });\n  const [tradeData, setTradeData] = useState<TradeData[]>([]);\n\n  const wsRef = useRef<WebSocket | null>(null);\n  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const subscriptionIdCounter = useRef(0);\n\n  const connect = useCallback(() => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      return;\n    }\n\n    setConnectionStatus('connecting');\n\n    try {\n      // Check if WebSocket is available in the browser\n      if (typeof WebSocket === 'undefined') {\n        console.error('WebSocket is not supported in this environment');\n        setConnectionStatus('error');\n        return;\n      }\n\n      const ws = new WebSocket(HYPERLIQUID_WS_URL);\n      wsRef.current = ws;\n\n      ws.onopen = () => {\n        console.log('WebSocket connected to Hyperliquid');\n        setConnectionStatus('connected');\n\n        // Resubscribe to active subscriptions\n        subscriptions.forEach(sub => {\n          if (sub.active) {\n            try {\n              ws.send(JSON.stringify({\n                method: 'subscribe',\n                subscription: sub.subscription\n              }));\n            } catch (error) {\n              console.error('Error sending subscription:', error);\n            }\n          }\n        });\n      };\n\n      ws.onmessage = (event) => {\n        try {\n          const message: HyperliquidWebSocketResponse = JSON.parse(event.data);\n          handleWebSocketMessage(message);\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n\n      ws.onclose = (event) => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        setConnectionStatus('disconnected');\n        wsRef.current = null;\n\n        // Auto-reconnect after 3 seconds if not manually disconnected\n        if (event.code !== 1000 && event.code !== 1001) {\n          reconnectTimeoutRef.current = setTimeout(() => {\n            console.log('Attempting to reconnect...');\n            connect();\n          }, 3000);\n        }\n      };\n\n      ws.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setConnectionStatus('error');\n      };\n\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      setConnectionStatus('error');\n    }\n  }, [subscriptions]);\n\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'Manual disconnect');\n      wsRef.current = null;\n    }\n    setConnectionStatus('disconnected');\n  }, []);\n\n  const handleWebSocketMessage = useCallback((message: HyperliquidWebSocketResponse) => {\n    const { channel, data } = message;\n\n    switch (channel) {\n      case 'trades':\n        if (data.trades) {\n          setTradeData(prev => [...prev.slice(-99), ...data.trades!].slice(-100));\n\n          // Update price data from trades\n          data.trades.forEach(trade => {\n            setPriceData(prev => {\n              const existing = prev.find(p => p.symbol === trade.symbol);\n              if (existing) {\n                return prev.map(p =>\n                  p.symbol === trade.symbol\n                    ? { ...p, price: trade.price, timestamp: trade.timestamp }\n                    : p\n                );\n              } else {\n                return [...prev, {\n                  symbol: trade.symbol,\n                  price: trade.price,\n                  timestamp: trade.timestamp\n                }];\n              }\n            });\n          });\n        }\n        break;\n\n      case 'l2Book':\n        if (data.levels && data.coin) {\n          const [bids, asks] = data.levels;\n          setOrderBookData({\n            symbol: data.coin,\n            bids: bids || [],\n            asks: asks || [],\n            timestamp: Date.now()\n          });\n        }\n        break;\n\n      case 'candle':\n        if (data.candle) {\n          setPriceData(prev => {\n            const existing = prev.find(p => p.symbol === data.candle!.symbol);\n            if (existing) {\n              return prev.map(p =>\n                p.symbol === data.candle!.symbol\n                  ? {\n                      ...p,\n                      price: data.candle!.close,\n                      timestamp: data.candle!.timestamp,\n                      volume: data.candle!.volume,\n                      high24h: data.candle!.high,\n                      low24h: data.candle!.low\n                    }\n                  : p\n              );\n            } else {\n              return [...prev, {\n                symbol: data.candle!.symbol,\n                price: data.candle!.close,\n                timestamp: data.candle!.timestamp,\n                volume: data.candle!.volume,\n                high24h: data.candle!.high,\n                low24h: data.candle!.low\n              }];\n            }\n          });\n        }\n        break;\n\n      default:\n        console.log('Unhandled channel:', channel, data);\n    }\n  }, []);\n\n  const subscribe = useCallback((subscription: HyperliquidSubscription) => {\n    const id = `sub_${subscriptionIdCounter.current++}`;\n\n    const newSubscription: SubscriptionState = {\n      id,\n      subscription,\n      active: true,\n      lastUpdate: Date.now()\n    };\n\n    setSubscriptions(prev => [...prev, newSubscription]);\n\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      try {\n        wsRef.current.send(JSON.stringify({\n          method: 'subscribe',\n          subscription\n        }));\n        console.log('Subscribed to:', subscription);\n      } catch (error) {\n        console.error('Error sending subscription:', error);\n      }\n    } else {\n      console.log('WebSocket not connected, subscription queued:', subscription);\n    }\n\n    return id;\n  }, []);\n\n  const unsubscribe = useCallback((subscriptionId: string) => {\n    setSubscriptions(prev => {\n      const subscription = prev.find(sub => sub.id === subscriptionId);\n      if (subscription && wsRef.current?.readyState === WebSocket.OPEN) {\n        wsRef.current.send(JSON.stringify({\n          method: 'unsubscribe',\n          subscription: subscription.subscription\n        }));\n      }\n      return prev.filter(sub => sub.id !== subscriptionId);\n    });\n  }, []);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n\n  return {\n    connectionStatus,\n    subscriptions: subscriptions.map(sub => sub.subscription),\n    priceData,\n    orderBookData,\n    tradeData,\n    connect,\n    disconnect,\n    subscribe,\n    unsubscribe\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAeA,MAAM,qBAAqB;AAEpB,SAAS;;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QACtD,8BAA8B;QAC9B;YACE,QAAQ;YACR,OAAO;YACP,WAAW,KAAK,GAAG;YACnB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,QAAQ;QACV;QACA;YACE,QAAQ;YACR,OAAO;YACP,WAAW,KAAK,GAAG;YACnB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,QAAQ;QACV;QACA;YACE,QAAQ;YACR,OAAO;YACP,WAAW,KAAK,GAAG;YACnB,QAAQ;YACR,WAAW,CAAC;YACZ,SAAS;YACT,QAAQ;QACV;KACD;IACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QACvE,QAAQ;QACR,MAAM;YACJ;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;SAC7B;QACD,MAAM;YACJ;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC5B;gBAAE,OAAO;gBAAO,MAAM;YAAM;SAC7B;QACD,WAAW,KAAK,GAAG;IACrB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAE1D,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IACvC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAC1D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAErC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAC1B,IAAI,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;gBAChD;YACF;YAEA,oBAAoB;YAEpB,IAAI;gBACF,iDAAiD;gBACjD,IAAI,OAAO,cAAc,aAAa;oBACpC,QAAQ,KAAK,CAAC;oBACd,oBAAoB;oBACpB;gBACF;gBAEA,MAAM,KAAK,IAAI,UAAU;gBACzB,MAAM,OAAO,GAAG;gBAEhB,GAAG,MAAM;oEAAG;wBACV,QAAQ,GAAG,CAAC;wBACZ,oBAAoB;wBAEpB,sCAAsC;wBACtC,cAAc,OAAO;4EAAC,CAAA;gCACpB,IAAI,IAAI,MAAM,EAAE;oCACd,IAAI;wCACF,GAAG,IAAI,CAAC,KAAK,SAAS,CAAC;4CACrB,QAAQ;4CACR,cAAc,IAAI,YAAY;wCAChC;oCACF,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,+BAA+B;oCAC/C;gCACF;4BACF;;oBACF;;gBAEA,GAAG,SAAS;oEAAG,CAAC;wBACd,IAAI;4BACF,MAAM,UAAwC,KAAK,KAAK,CAAC,MAAM,IAAI;4BACnE,uBAAuB;wBACzB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,oCAAoC;wBACpD;oBACF;;gBAEA,GAAG,OAAO;oEAAG,CAAC;wBACZ,QAAQ,GAAG,CAAC,2BAA2B,MAAM,IAAI,EAAE,MAAM,MAAM;wBAC/D,oBAAoB;wBACpB,MAAM,OAAO,GAAG;wBAEhB,8DAA8D;wBAC9D,IAAI,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,MAAM;4BAC9C,oBAAoB,OAAO,GAAG;gFAAW;oCACvC,QAAQ,GAAG,CAAC;oCACZ;gCACF;+EAAG;wBACL;oBACF;;gBAEA,GAAG,OAAO;oEAAG,CAAC;wBACZ,QAAQ,KAAK,CAAC,oBAAoB;wBAClC,oBAAoB;oBACtB;;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,oBAAoB;YACtB;QACF;uDAAG;QAAC;KAAc;IAElB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YAC7B,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,aAAa,oBAAoB,OAAO;gBACxC,oBAAoB,OAAO,GAAG;YAChC;YAEA,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM;gBAC1B,MAAM,OAAO,GAAG;YAClB;YACA,oBAAoB;QACtB;0DAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uEAAE,CAAC;YAC1C,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG;YAE1B,OAAQ;gBACN,KAAK;oBACH,IAAI,KAAK,MAAM,EAAE;wBACf;2FAAa,CAAA,OAAQ;uCAAI,KAAK,KAAK,CAAC,CAAC;uCAAQ,KAAK,MAAM;iCAAE,CAAC,KAAK,CAAC,CAAC;;wBAElE,gCAAgC;wBAChC,KAAK,MAAM,CAAC,OAAO;2FAAC,CAAA;gCAClB;mGAAa,CAAA;wCACX,MAAM,WAAW,KAAK,IAAI;oHAAC,CAAA,IAAK,EAAE,MAAM,KAAK,MAAM,MAAM;;wCACzD,IAAI,UAAU;4CACZ,OAAO,KAAK,GAAG;+GAAC,CAAA,IACd,EAAE,MAAM,KAAK,MAAM,MAAM,GACrB;wDAAE,GAAG,CAAC;wDAAE,OAAO,MAAM,KAAK;wDAAE,WAAW,MAAM,SAAS;oDAAC,IACvD;;wCAER,OAAO;4CACL,OAAO;mDAAI;gDAAM;oDACf,QAAQ,MAAM,MAAM;oDACpB,OAAO,MAAM,KAAK;oDAClB,WAAW,MAAM,SAAS;gDAC5B;6CAAE;wCACJ;oCACF;;4BACF;;oBACF;oBACA;gBAEF,KAAK;oBACH,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE;wBAC5B,MAAM,CAAC,MAAM,KAAK,GAAG,KAAK,MAAM;wBAChC,iBAAiB;4BACf,QAAQ,KAAK,IAAI;4BACjB,MAAM,QAAQ,EAAE;4BAChB,MAAM,QAAQ,EAAE;4BAChB,WAAW,KAAK,GAAG;wBACrB;oBACF;oBACA;gBAEF,KAAK;oBACH,IAAI,KAAK,MAAM,EAAE;wBACf;2FAAa,CAAA;gCACX,MAAM,WAAW,KAAK,IAAI;4GAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,MAAM,CAAE,MAAM;;gCAChE,IAAI,UAAU;oCACZ,OAAO,KAAK,GAAG;uGAAC,CAAA,IACd,EAAE,MAAM,KAAK,KAAK,MAAM,CAAE,MAAM,GAC5B;gDACE,GAAG,CAAC;gDACJ,OAAO,KAAK,MAAM,CAAE,KAAK;gDACzB,WAAW,KAAK,MAAM,CAAE,SAAS;gDACjC,QAAQ,KAAK,MAAM,CAAE,MAAM;gDAC3B,SAAS,KAAK,MAAM,CAAE,IAAI;gDAC1B,QAAQ,KAAK,MAAM,CAAE,GAAG;4CAC1B,IACA;;gCAER,OAAO;oCACL,OAAO;2CAAI;wCAAM;4CACf,QAAQ,KAAK,MAAM,CAAE,MAAM;4CAC3B,OAAO,KAAK,MAAM,CAAE,KAAK;4CACzB,WAAW,KAAK,MAAM,CAAE,SAAS;4CACjC,QAAQ,KAAK,MAAM,CAAE,MAAM;4CAC3B,SAAS,KAAK,MAAM,CAAE,IAAI;4CAC1B,QAAQ,KAAK,MAAM,CAAE,GAAG;wCAC1B;qCAAE;gCACJ;4BACF;;oBACF;oBACA;gBAEF;oBACE,QAAQ,GAAG,CAAC,sBAAsB,SAAS;YAC/C;QACF;sEAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YAC7B,MAAM,KAAK,CAAC,IAAI,EAAE,sBAAsB,OAAO,IAAI;YAEnD,MAAM,kBAAqC;gBACzC;gBACA;gBACA,QAAQ;gBACR,YAAY,KAAK,GAAG;YACtB;YAEA;kEAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAgB;;YAEnD,IAAI,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;gBAChD,IAAI;oBACF,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;wBAChC,QAAQ;wBACR;oBACF;oBACA,QAAQ,GAAG,CAAC,kBAAkB;gBAChC,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,iDAAiD;YAC/D;YAEA,OAAO;QACT;yDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YAC/B;oEAAiB,CAAA;oBACf,MAAM,eAAe,KAAK,IAAI;yFAAC,CAAA,MAAO,IAAI,EAAE,KAAK;;oBACjD,IAAI,gBAAgB,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;wBAChE,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;4BAChC,QAAQ;4BACR,cAAc,aAAa,YAAY;wBACzC;oBACF;oBACA,OAAO,KAAK,MAAM;4EAAC,CAAA,MAAO,IAAI,EAAE,KAAK;;gBACvC;;QACF;2DAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR;qDAAO;oBACL;gBACF;;QACF;4CAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA,eAAe,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,YAAY;QACxD;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAvRgB"}}, {"offset": {"line": 5092, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5098, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/hooks/useWalletTracker.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { WalletData, WalletTrade, ScalpingMetrics, LiveTradingActivity, RealTimeUpdate } from '@/types/hyperliquid';\n\n// Mock data generator for demonstration\nconst generateMockWalletData = (): WalletData[] => {\n  const addresses = [\n    '******************************************',\n    '0x8ba1f109551bD432803012645Hac136c22C501e',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '0xfedcbafedcbafedcbafedcbafedcbafedcbafedcba',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n    '******************************************',\n  ];\n\n  const nicknames = [\n    'Whale Hunter', 'Scalp Master', 'Quick Trader', 'Volume King',\n    'Profit Wizard', 'Speed Demon', 'Market Maker', 'Trend Rider',\n    'Risk Taker', 'Smart Money', 'Flash Trader', 'Momentum Player',\n    'Arbitrage Pro', 'Swing Master', 'Day Trader'\n  ];\n\n  return addresses.map((address, index) => {\n    const isScalper = Math.random() > 0.6; // 40% chance of being a scalper\n    const volume24h = Math.random() * 5000000 + 10000; // $10K to $5M\n    const pnl24h = (Math.random() - 0.3) * volume24h * 0.1; // -30% to +70% of 10% volume\n    const pnlAllTime = pnl24h * (Math.random() * 100 + 10); // 10-110 days worth\n    const tradeCount = isScalper ?\n      Math.floor(Math.random() * 200 + 50) : // Scalpers: 50-250 trades\n      Math.floor(Math.random() * 50 + 5);    // Others: 5-55 trades\n\n    const avgTradeSize = volume24h / tradeCount;\n    const maxTradeSize = avgTradeSize * (Math.random() * 5 + 2); // 2-7x avg\n    const minTradeSize = avgTradeSize * (Math.random() * 0.5 + 0.1); // 0.1-0.6x avg\n\n    const winRate = Math.random() * 40 + 40; // 40-80% win rate\n    const scalpingScore = isScalper ?\n      Math.floor(Math.random() * 40 + 60) : // Scalpers: 60-100\n      Math.floor(Math.random() * 60 + 10);   // Others: 10-70\n\n    const tags = [];\n    if (isScalper) tags.push('scalper');\n    if (volume24h > 1000000) tags.push('whale');\n    if (pnl24h > 50000) tags.push('profitable');\n    if (tradeCount > 100) tags.push('active');\n    if (winRate > 70) tags.push('skilled');\n\n    return {\n      address,\n      nickname: Math.random() > 0.3 ? nicknames[index % nicknames.length] : undefined,\n      totalVolume24h: volume24h,\n      totalPnl24h: pnl24h,\n      totalPnlAllTime: pnlAllTime,\n      tradeCount24h: tradeCount,\n      avgTradeSize,\n      maxTradeSize,\n      minTradeSize,\n      winRate,\n      lastTradeTime: Date.now() - Math.random() * 3600000, // Within last hour\n      isScalper,\n      scalpingScore,\n      tags\n    };\n  });\n};\n\nconst generateMockTrades = (wallets: WalletData[]): WalletTrade[] => {\n  const trades: WalletTrade[] = [];\n  const symbols = ['HYPER', 'BTC', 'ETH', 'SOL', 'AVAX'];\n  const orderTypes = ['market', 'limit', 'stop', 'stop_limit', 'take_profit', 'trailing_stop'] as const;\n  const timeInForceOptions = ['GTC', 'IOC', 'FOK', 'GTD'] as const;\n\n  wallets.forEach(wallet => {\n    const tradeCount = Math.min(wallet.tradeCount24h, 20); // Show last 20 trades max\n    let currentPosition: Record<string, number> = {}; // Track positions per symbol\n\n    for (let i = 0; i < tradeCount; i++) {\n      const symbol = symbols[Math.floor(Math.random() * symbols.length)];\n      const side = Math.random() > 0.5 ? 'buy' : 'sell';\n      const size = Math.random() * 100 + 1;\n      const price = symbol === 'HYPER' ? 28.5 + (Math.random() - 0.5) * 2 :\n                   symbol === 'BTC' ? 45000 + (Math.random() - 0.5) * 1000 :\n                   symbol === 'ETH' ? 3200 + (Math.random() - 0.5) * 200 :\n                   symbol === 'SOL' ? 100 + (Math.random() - 0.5) * 10 :\n                   50 + (Math.random() - 0.5) * 5;\n\n      const value = size * price;\n      const pnl = (Math.random() - 0.4) * value * 0.05; // -40% to +60% of 5% value\n      const fee = value * 0.001; // 0.1% fee\n\n      // Determine position side and changes\n      const currentPos = currentPosition[symbol] || 0;\n      const positionChange = side === 'buy' ? size : -size;\n      const newPosition = currentPos + positionChange;\n      currentPosition[symbol] = newPosition;\n\n      const isClosingPosition = (currentPos > 0 && side === 'sell') || (currentPos < 0 && side === 'buy');\n      const positionSide = newPosition > 0 ? 'long' : newPosition < 0 ? 'short' : 'neutral';\n\n      // Generate order details\n      const orderType = orderTypes[Math.floor(Math.random() * orderTypes.length)];\n      const isScalper = wallet.isScalper;\n      const hasStopLoss = Math.random() > (isScalper ? 0.7 : 0.4); // Scalpers use less stop losses\n      const hasTakeProfit = Math.random() > (isScalper ? 0.5 : 0.3);\n      const leverage = Math.random() > 0.6 ? Math.floor(Math.random() * 10) + 1 : undefined;\n\n      const stopLoss = hasStopLoss ?\n        (side === 'buy' ? price * (0.95 - Math.random() * 0.05) : price * (1.05 + Math.random() * 0.05)) :\n        undefined;\n\n      const takeProfit = hasTakeProfit ?\n        (side === 'buy' ? price * (1.02 + Math.random() * 0.08) : price * (0.98 - Math.random() * 0.08)) :\n        undefined;\n\n      const riskRewardRatio = (hasStopLoss && hasTakeProfit) ?\n        Math.abs((takeProfit! - price) / (stopLoss! - price)) : undefined;\n\n      const executionType = orderType === 'market' ? 'taker' :\n                           (Math.random() > 0.6 ? 'maker' : 'taker');\n\n      const positionSizing = value > 50000 ? 'aggressive' :\n                            value > 10000 ? 'moderate' : 'conservative';\n\n      trades.push({\n        walletAddress: wallet.address,\n        symbol,\n        side,\n        size,\n        price,\n        value,\n        timestamp: Date.now() - Math.random() * 86400000, // Within last 24h\n        pnl,\n        fee,\n        tradeId: `trade_${wallet.address}_${i}`,\n        orderDetails: {\n          orderType,\n          positionSide,\n          leverage,\n          stopLoss,\n          takeProfit,\n          trailingStopDistance: orderType === 'trailing_stop' ? Math.random() * 2 + 0.5 : undefined,\n          timeInForce: timeInForceOptions[Math.floor(Math.random() * timeInForceOptions.length)],\n          reduceOnly: isClosingPosition && Math.random() > 0.5,\n          postOnly: orderType === 'limit' && Math.random() > 0.7,\n          triggerPrice: ['stop', 'stop_limit', 'take_profit'].includes(orderType) ?\n            price * (0.98 + Math.random() * 0.04) : undefined,\n          executionType,\n          slippage: executionType === 'taker' ? Math.random() * 0.002 : undefined,\n          partialFills: Math.floor(Math.random() * 3),\n          averageFillPrice: price * (0.999 + Math.random() * 0.002)\n        },\n        riskManagement: {\n          hasStopLoss,\n          hasTakeProfit,\n          riskRewardRatio,\n          maxDrawdown: Math.random() * 0.15, // 0-15% max drawdown\n          positionSizing,\n          hedged: Math.random() > 0.8, // 20% chance of being hedged\n          marginUsed: leverage ? value / leverage : value,\n          liquidationPrice: leverage ?\n            (side === 'buy' ? price * (1 - 0.8/leverage) : price * (1 + 0.8/leverage)) :\n            undefined\n        },\n        relatedOrders: hasStopLoss || hasTakeProfit ?\n          [`sl_${wallet.address}_${i}`, `tp_${wallet.address}_${i}`].filter((_, idx) =>\n            (idx === 0 && hasStopLoss) || (idx === 1 && hasTakeProfit)\n          ) : undefined,\n        isClosingPosition,\n        positionChange\n      });\n    }\n  });\n\n  return trades.sort((a, b) => b.timestamp - a.timestamp);\n};\n\nconst generateLiveActivity = (wallets: WalletData[]): LiveTradingActivity[] => {\n  return wallets.map(wallet => {\n    const now = Date.now();\n    const lastTradeTime = now - Math.random() * 3600000; // Within last hour\n    const isActiveNow = (now - lastTradeTime) < 300000; // Active if traded in last 5 minutes\n\n    const tradesInLastHour = Math.floor(Math.random() * 20);\n    const tradesInLast5Min = isActiveNow ? Math.floor(Math.random() * 5) : 0;\n    const volumeInLastHour = tradesInLastHour * (Math.random() * 10000 + 1000);\n    const pnlInLastHour = (Math.random() - 0.4) * volumeInLastHour * 0.05;\n\n    const currentStreak = Math.floor(Math.random() * 8);\n    const streakType = currentStreak === 0 ? 'neutral' :\n                      Math.random() > 0.5 ? 'winning' : 'losing';\n\n    // Calculate hot score based on activity\n    let hotScore = 0;\n    if (isActiveNow) hotScore += 30;\n    if (tradesInLastHour > 10) hotScore += 25;\n    if (volumeInLastHour > 50000) hotScore += 20;\n    if (pnlInLastHour > 0) hotScore += 15;\n    if (wallet.isScalper) hotScore += 10;\n    hotScore = Math.min(100, hotScore + Math.random() * 20);\n\n    const momentum = pnlInLastHour > 1000 ? 'bullish' :\n                    pnlInLastHour < -1000 ? 'bearish' : 'neutral';\n\n    const riskLevel = volumeInLastHour > 100000 ? 'extreme' :\n                     volumeInLastHour > 50000 ? 'high' :\n                     volumeInLastHour > 20000 ? 'medium' : 'low';\n\n    return {\n      walletAddress: wallet.address,\n      isActiveNow,\n      lastTradeTime,\n      tradesInLastHour,\n      tradesInLast5Min,\n      volumeInLastHour,\n      pnlInLastHour,\n      currentStreak,\n      streakType,\n      hotScore,\n      momentum,\n      riskLevel\n    };\n  });\n};\n\nexport function useWalletTracker() {\n  const [wallets, setWallets] = useState<WalletData[]>([]);\n  const [walletTrades, setWalletTrades] = useState<WalletTrade[]>([]);\n  const [liveActivity, setLiveActivity] = useState<LiveTradingActivity[]>([]);\n  const [trackedWallets, setTrackedWallets] = useState<Set<string>>(new Set());\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Initialize with mock data\n  useEffect(() => {\n    const mockWallets = generateMockWalletData();\n    const mockTrades = generateMockTrades(mockWallets);\n    const mockLiveActivity = generateLiveActivity(mockWallets);\n\n    setWallets(mockWallets);\n    setWalletTrades(mockTrades);\n    setLiveActivity(mockLiveActivity);\n  }, []);\n\n  const addWallet = useCallback((address: string, nickname?: string) => {\n    if (trackedWallets.has(address)) {\n      console.log('Wallet already being tracked');\n      return;\n    }\n\n    setIsLoading(true);\n\n    // Simulate API call\n    setTimeout(() => {\n      // Generate mock data for new wallet\n      const volume24h = Math.random() * 1000000 + 5000;\n      const pnl24h = (Math.random() - 0.3) * volume24h * 0.08;\n      const isScalper = Math.random() > 0.7;\n      const tradeCount = isScalper ?\n        Math.floor(Math.random() * 150 + 30) :\n        Math.floor(Math.random() * 30 + 3);\n\n      const newWallet: WalletData = {\n        address,\n        nickname,\n        totalVolume24h: volume24h,\n        totalPnl24h: pnl24h,\n        totalPnlAllTime: pnl24h * (Math.random() * 50 + 5),\n        tradeCount24h: tradeCount,\n        avgTradeSize: volume24h / tradeCount,\n        maxTradeSize: volume24h / tradeCount * (Math.random() * 4 + 2),\n        minTradeSize: volume24h / tradeCount * (Math.random() * 0.4 + 0.1),\n        winRate: Math.random() * 35 + 45,\n        lastTradeTime: Date.now() - Math.random() * 1800000,\n        isScalper,\n        scalpingScore: isScalper ? Math.floor(Math.random() * 35 + 65) : Math.floor(Math.random() * 50 + 15),\n        tags: isScalper ? ['scalper', 'tracked'] : ['tracked']\n      };\n\n      setWallets(prev => [...prev, newWallet]);\n      setTrackedWallets(prev => new Set([...prev, address]));\n      setIsLoading(false);\n    }, 1000);\n  }, [trackedWallets]);\n\n  const removeWallet = useCallback((address: string) => {\n    setWallets(prev => prev.filter(w => w.address !== address));\n    setTrackedWallets(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(address);\n      return newSet;\n    });\n    setWalletTrades(prev => prev.filter(t => t.walletAddress !== address));\n  }, []);\n\n  const getWalletTrades = useCallback((address: string): WalletTrade[] => {\n    return walletTrades.filter(trade => trade.walletAddress === address);\n  }, [walletTrades]);\n\n  const calculateScalpingMetrics = useCallback((address: string): ScalpingMetrics => {\n    const trades = getWalletTrades(address).sort((a, b) => a.timestamp - b.timestamp);\n\n    if (trades.length < 2) {\n      return {\n        avgTimeBetweenTrades: 0,\n        quickTradeRatio: 0,\n        smallTradeRatio: 0,\n        reversalRate: 0\n      };\n    }\n\n    // Calculate average time between trades\n    const timeDiffs = [];\n    for (let i = 1; i < trades.length; i++) {\n      timeDiffs.push((trades[i].timestamp - trades[i-1].timestamp) / (1000 * 60)); // minutes\n    }\n    const avgTimeBetweenTrades = timeDiffs.reduce((a, b) => a + b, 0) / timeDiffs.length;\n\n    // Quick trades (under 5 minutes)\n    const quickTrades = timeDiffs.filter(diff => diff < 5).length;\n    const quickTradeRatio = (quickTrades / timeDiffs.length) * 100;\n\n    // Small trades (under $1000)\n    const smallTrades = trades.filter(trade => trade.value < 1000).length;\n    const smallTradeRatio = (smallTrades / trades.length) * 100;\n\n    // Reversal rate (buy followed by sell or vice versa within 10 minutes)\n    let reversals = 0;\n    for (let i = 1; i < trades.length; i++) {\n      const timeDiff = (trades[i].timestamp - trades[i-1].timestamp) / (1000 * 60);\n      if (timeDiff < 10 && trades[i].side !== trades[i-1].side) {\n        reversals++;\n      }\n    }\n    const reversalRate = (reversals / (trades.length - 1)) * 100;\n\n    return {\n      avgTimeBetweenTrades,\n      quickTradeRatio,\n      smallTradeRatio,\n      reversalRate\n    };\n  }, [getWalletTrades]);\n\n  // Simulate real-time updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setWallets(prev => prev.map(wallet => {\n        // Small random updates to simulate real-time data\n        const volumeChange = (Math.random() - 0.5) * wallet.totalVolume24h * 0.01;\n        const pnlChange = (Math.random() - 0.5) * Math.abs(wallet.totalPnl24h) * 0.05;\n\n        return {\n          ...wallet,\n          totalVolume24h: Math.max(0, wallet.totalVolume24h + volumeChange),\n          totalPnl24h: wallet.totalPnl24h + pnlChange,\n          lastTradeTime: Math.random() > 0.9 ? Date.now() : wallet.lastTradeTime\n        };\n      }));\n\n      // Update live activity\n      setLiveActivity(prev => prev.map(activity => {\n        const volumeChange = (Math.random() - 0.5) * activity.volumeInLastHour * 0.1;\n        const pnlChange = (Math.random() - 0.5) * Math.abs(activity.pnlInLastHour) * 0.1;\n        const newTradesInLast5Min = Math.random() > 0.8 ? Math.floor(Math.random() * 3) : activity.tradesInLast5Min;\n        const isActiveNow = newTradesInLast5Min > 0 || Math.random() > 0.85;\n\n        // Recalculate hot score\n        let hotScore = activity.hotScore;\n        if (isActiveNow && !activity.isActiveNow) hotScore += 20;\n        if (!isActiveNow && activity.isActiveNow) hotScore -= 15;\n        hotScore = Math.max(0, Math.min(100, hotScore + (Math.random() - 0.5) * 10));\n\n        return {\n          ...activity,\n          isActiveNow,\n          tradesInLast5Min: newTradesInLast5Min,\n          volumeInLastHour: Math.max(0, activity.volumeInLastHour + volumeChange),\n          pnlInLastHour: activity.pnlInLastHour + pnlChange,\n          hotScore,\n          lastTradeTime: isActiveNow ? Date.now() : activity.lastTradeTime\n        };\n      }));\n    }, 3000); // Update every 3 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return {\n    wallets,\n    walletTrades,\n    liveActivity,\n    trackedWallets,\n    isLoading,\n    addWallet,\n    removeWallet,\n    getWalletTrades,\n    calculateScalpingMetrics\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAKA,wCAAwC;AACxC,MAAM,yBAAyB;IAC7B,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY;QAChB;QAAgB;QAAgB;QAAgB;QAChD;QAAiB;QAAe;QAAgB;QAChD;QAAc;QAAe;QAAgB;QAC7C;QAAiB;QAAgB;KAClC;IAED,OAAO,UAAU,GAAG,CAAC,CAAC,SAAS;QAC7B,MAAM,YAAY,KAAK,MAAM,KAAK,KAAK,gCAAgC;QACvE,MAAM,YAAY,KAAK,MAAM,KAAK,UAAU,OAAO,cAAc;QACjE,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY,KAAK,6BAA6B;QACrF,MAAM,aAAa,SAAS,CAAC,KAAK,MAAM,KAAK,MAAM,EAAE,GAAG,oBAAoB;QAC5E,MAAM,aAAa,YACjB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MACjC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,IAAO,sBAAsB;QAE/D,MAAM,eAAe,YAAY;QACjC,MAAM,eAAe,eAAe,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,GAAG,WAAW;QACxE,MAAM,eAAe,eAAe,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,GAAG,eAAe;QAEhF,MAAM,UAAU,KAAK,MAAM,KAAK,KAAK,IAAI,kBAAkB;QAC3D,MAAM,gBAAgB,YACpB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAChC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,KAAO,gBAAgB;QAEzD,MAAM,OAAO,EAAE;QACf,IAAI,WAAW,KAAK,IAAI,CAAC;QACzB,IAAI,YAAY,SAAS,KAAK,IAAI,CAAC;QACnC,IAAI,SAAS,OAAO,KAAK,IAAI,CAAC;QAC9B,IAAI,aAAa,KAAK,KAAK,IAAI,CAAC;QAChC,IAAI,UAAU,IAAI,KAAK,IAAI,CAAC;QAE5B,OAAO;YACL;YACA,UAAU,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,QAAQ,UAAU,MAAM,CAAC,GAAG;YACtE,gBAAgB;YAChB,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf;YACA;YACA;YACA;YACA,eAAe,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK;YAC5C;YACA;YACA;QACF;IACF;AACF;AAEA,MAAM,qBAAqB,CAAC;IAC1B,MAAM,SAAwB,EAAE;IAChC,MAAM,UAAU;QAAC;QAAS;QAAO;QAAO;QAAO;KAAO;IACtD,MAAM,aAAa;QAAC;QAAU;QAAS;QAAQ;QAAc;QAAe;KAAgB;IAC5F,MAAM,qBAAqB;QAAC;QAAO;QAAO;QAAO;KAAM;IAEvD,QAAQ,OAAO,CAAC,CAAA;QACd,MAAM,aAAa,KAAK,GAAG,CAAC,OAAO,aAAa,EAAE,KAAK,0BAA0B;QACjF,IAAI,kBAA0C,CAAC,GAAG,6BAA6B;QAE/E,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACnC,MAAM,SAAS,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;YAClE,MAAM,OAAO,KAAK,MAAM,KAAK,MAAM,QAAQ;YAC3C,MAAM,OAAO,KAAK,MAAM,KAAK,MAAM;YACnC,MAAM,QAAQ,WAAW,UAAU,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACrD,WAAW,QAAQ,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,OACnD,WAAW,QAAQ,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,MAClD,WAAW,QAAQ,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KACjD,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAE1C,MAAM,QAAQ,OAAO;YACrB,MAAM,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,MAAM,2BAA2B;YAC7E,MAAM,MAAM,QAAQ,OAAO,WAAW;YAEtC,sCAAsC;YACtC,MAAM,aAAa,eAAe,CAAC,OAAO,IAAI;YAC9C,MAAM,iBAAiB,SAAS,QAAQ,OAAO,CAAC;YAChD,MAAM,cAAc,aAAa;YACjC,eAAe,CAAC,OAAO,GAAG;YAE1B,MAAM,oBAAoB,AAAC,aAAa,KAAK,SAAS,UAAY,aAAa,KAAK,SAAS;YAC7F,MAAM,eAAe,cAAc,IAAI,SAAS,cAAc,IAAI,UAAU;YAE5E,yBAAyB;YACzB,MAAM,YAAY,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;YAC3E,MAAM,YAAY,OAAO,SAAS;YAClC,MAAM,cAAc,KAAK,MAAM,KAAK,CAAC,YAAY,MAAM,GAAG,GAAG,gCAAgC;YAC7F,MAAM,gBAAgB,KAAK,MAAM,KAAK,CAAC,YAAY,MAAM,GAAG;YAC5D,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI;YAE5E,MAAM,WAAW,cACd,SAAS,QAAQ,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAC9F;YAEF,MAAM,aAAa,gBAChB,SAAS,QAAQ,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAC9F;YAEF,MAAM,kBAAkB,AAAC,eAAe,gBACtC,KAAK,GAAG,CAAC,CAAC,aAAc,KAAK,IAAI,CAAC,WAAY,KAAK,KAAK;YAE1D,MAAM,gBAAgB,cAAc,WAAW,UACzB,KAAK,MAAM,KAAK,MAAM,UAAU;YAEtD,MAAM,iBAAiB,QAAQ,QAAQ,eACjB,QAAQ,QAAQ,aAAa;YAEnD,OAAO,IAAI,CAAC;gBACV,eAAe,OAAO,OAAO;gBAC7B;gBACA;gBACA;gBACA;gBACA;gBACA,WAAW,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK;gBACxC;gBACA;gBACA,SAAS,CAAC,MAAM,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,GAAG;gBACvC,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA,sBAAsB,cAAc,kBAAkB,KAAK,MAAM,KAAK,IAAI,MAAM;oBAChF,aAAa,kBAAkB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,mBAAmB,MAAM,EAAE;oBACtF,YAAY,qBAAqB,KAAK,MAAM,KAAK;oBACjD,UAAU,cAAc,WAAW,KAAK,MAAM,KAAK;oBACnD,cAAc;wBAAC;wBAAQ;wBAAc;qBAAc,CAAC,QAAQ,CAAC,aAC3D,QAAQ,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI;oBAC1C;oBACA,UAAU,kBAAkB,UAAU,KAAK,MAAM,KAAK,QAAQ;oBAC9D,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oBACzC,kBAAkB,QAAQ,CAAC,QAAQ,KAAK,MAAM,KAAK,KAAK;gBAC1D;gBACA,gBAAgB;oBACd;oBACA;oBACA;oBACA,aAAa,KAAK,MAAM,KAAK;oBAC7B;oBACA,QAAQ,KAAK,MAAM,KAAK;oBACxB,YAAY,WAAW,QAAQ,WAAW;oBAC1C,kBAAkB,WACf,SAAS,QAAQ,QAAQ,CAAC,IAAI,MAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,MAAI,QAAQ,IACxE;gBACJ;gBACA,eAAe,eAAe,gBAC5B;oBAAC,CAAC,GAAG,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,GAAG;oBAAE,CAAC,GAAG,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,GAAG;iBAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MACpE,AAAC,QAAQ,KAAK,eAAiB,QAAQ,KAAK,iBAC1C;gBACN;gBACA;YACF;QACF;IACF;IAEA,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;AACxD;AAEA,MAAM,uBAAuB,CAAC;IAC5B,OAAO,QAAQ,GAAG,CAAC,CAAA;QACjB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,gBAAgB,MAAM,KAAK,MAAM,KAAK,SAAS,mBAAmB;QACxE,MAAM,cAAc,AAAC,MAAM,gBAAiB,QAAQ,qCAAqC;QAEzF,MAAM,mBAAmB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACpD,MAAM,mBAAmB,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;QACvE,MAAM,mBAAmB,mBAAmB,CAAC,KAAK,MAAM,KAAK,QAAQ,IAAI;QACzE,MAAM,gBAAgB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,mBAAmB;QAEjE,MAAM,gBAAgB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACjD,MAAM,aAAa,kBAAkB,IAAI,YACvB,KAAK,MAAM,KAAK,MAAM,YAAY;QAEpD,wCAAwC;QACxC,IAAI,WAAW;QACf,IAAI,aAAa,YAAY;QAC7B,IAAI,mBAAmB,IAAI,YAAY;QACvC,IAAI,mBAAmB,OAAO,YAAY;QAC1C,IAAI,gBAAgB,GAAG,YAAY;QACnC,IAAI,OAAO,SAAS,EAAE,YAAY;QAClC,WAAW,KAAK,GAAG,CAAC,KAAK,WAAW,KAAK,MAAM,KAAK;QAEpD,MAAM,WAAW,gBAAgB,OAAO,YACxB,gBAAgB,CAAC,OAAO,YAAY;QAEpD,MAAM,YAAY,mBAAmB,SAAS,YAC7B,mBAAmB,QAAQ,SAC3B,mBAAmB,QAAQ,WAAW;QAEvD,OAAO;YACL,eAAe,OAAO,OAAO;YAC7B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;AACF;AAEO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,cAAc;YACpB,MAAM,aAAa,mBAAmB;YACtC,MAAM,mBAAmB,qBAAqB;YAE9C,WAAW;YACX,gBAAgB;YAChB,gBAAgB;QAClB;qCAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,SAAiB;YAC9C,IAAI,eAAe,GAAG,CAAC,UAAU;gBAC/B,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,aAAa;YAEb,oBAAoB;YACpB;2DAAW;oBACT,oCAAoC;oBACpC,MAAM,YAAY,KAAK,MAAM,KAAK,UAAU;oBAC5C,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBACnD,MAAM,YAAY,KAAK,MAAM,KAAK;oBAClC,MAAM,aAAa,YACjB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MACjC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;oBAElC,MAAM,YAAwB;wBAC5B;wBACA;wBACA,gBAAgB;wBAChB,aAAa;wBACb,iBAAiB,SAAS,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC;wBACjD,eAAe;wBACf,cAAc,YAAY;wBAC1B,cAAc,YAAY,aAAa,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC;wBAC7D,cAAc,YAAY,aAAa,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG;wBACjE,SAAS,KAAK,MAAM,KAAK,KAAK;wBAC9B,eAAe,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK;wBAC5C;wBACA,eAAe,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;wBACjG,MAAM,YAAY;4BAAC;4BAAW;yBAAU,GAAG;4BAAC;yBAAU;oBACxD;oBAEA;mEAAW,CAAA,OAAQ;mCAAI;gCAAM;6BAAU;;oBACvC;mEAAkB,CAAA,OAAQ,IAAI,IAAI;mCAAI;gCAAM;6BAAQ;;oBACpD,aAAa;gBACf;0DAAG;QACL;kDAAG;QAAC;KAAe;IAEnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAChC;8DAAW,CAAA,OAAQ,KAAK,MAAM;sEAAC,CAAA,IAAK,EAAE,OAAO,KAAK;;;YAClD;8DAAkB,CAAA;oBAChB,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,MAAM,CAAC;oBACd,OAAO;gBACT;;YACA;8DAAgB,CAAA,OAAQ,KAAK,MAAM;sEAAC,CAAA,IAAK,EAAE,aAAa,KAAK;;;QAC/D;qDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACnC,OAAO,aAAa,MAAM;iEAAC,CAAA,QAAS,MAAM,aAAa,KAAK;;QAC9D;wDAAG;QAAC;KAAa;IAEjB,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE,CAAC;YAC5C,MAAM,SAAS,gBAAgB,SAAS,IAAI;iFAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;;YAEhF,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,OAAO;oBACL,sBAAsB;oBACtB,iBAAiB;oBACjB,iBAAiB;oBACjB,cAAc;gBAChB;YACF;YAEA,wCAAwC;YACxC,MAAM,YAAY,EAAE;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,GAAG,MAAM,CAAC,IAAE,EAAE,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,UAAU;YACzF;YACA,MAAM,uBAAuB,UAAU,MAAM;0EAAC,CAAC,GAAG,IAAM,IAAI;yEAAG,KAAK,UAAU,MAAM;YAEpF,iCAAiC;YACjC,MAAM,cAAc,UAAU,MAAM;0EAAC,CAAA,OAAQ,OAAO;yEAAG,MAAM;YAC7D,MAAM,kBAAkB,AAAC,cAAc,UAAU,MAAM,GAAI;YAE3D,6BAA6B;YAC7B,MAAM,cAAc,OAAO,MAAM;0EAAC,CAAA,QAAS,MAAM,KAAK,GAAG;yEAAM,MAAM;YACrE,MAAM,kBAAkB,AAAC,cAAc,OAAO,MAAM,GAAI;YAExD,uEAAuE;YACvE,IAAI,YAAY;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,MAAM,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,GAAG,MAAM,CAAC,IAAE,EAAE,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;gBAC3E,IAAI,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,IAAE,EAAE,CAAC,IAAI,EAAE;oBACxD;gBACF;YACF;YACA,MAAM,eAAe,AAAC,YAAY,CAAC,OAAO,MAAM,GAAG,CAAC,IAAK;YAEzD,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QACF;iEAAG;QAAC;KAAgB;IAEpB,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW;uDAAY;oBAC3B;+DAAW,CAAA,OAAQ,KAAK,GAAG;uEAAC,CAAA;oCAC1B,kDAAkD;oCAClD,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,OAAO,cAAc,GAAG;oCACrE,MAAM,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,OAAO,WAAW,IAAI;oCAEzE,OAAO;wCACL,GAAG,MAAM;wCACT,gBAAgB,KAAK,GAAG,CAAC,GAAG,OAAO,cAAc,GAAG;wCACpD,aAAa,OAAO,WAAW,GAAG;wCAClC,eAAe,KAAK,MAAM,KAAK,MAAM,KAAK,GAAG,KAAK,OAAO,aAAa;oCACxE;gCACF;;;oBAEA,uBAAuB;oBACvB;+DAAgB,CAAA,OAAQ,KAAK,GAAG;uEAAC,CAAA;oCAC/B,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,SAAS,gBAAgB,GAAG;oCACzE,MAAM,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,SAAS,aAAa,IAAI;oCAC7E,MAAM,sBAAsB,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,SAAS,gBAAgB;oCAC3G,MAAM,cAAc,sBAAsB,KAAK,KAAK,MAAM,KAAK;oCAE/D,wBAAwB;oCACxB,IAAI,WAAW,SAAS,QAAQ;oCAChC,IAAI,eAAe,CAAC,SAAS,WAAW,EAAE,YAAY;oCACtD,IAAI,CAAC,eAAe,SAAS,WAAW,EAAE,YAAY;oCACtD,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oCAExE,OAAO;wCACL,GAAG,QAAQ;wCACX;wCACA,kBAAkB;wCAClB,kBAAkB,KAAK,GAAG,CAAC,GAAG,SAAS,gBAAgB,GAAG;wCAC1D,eAAe,SAAS,aAAa,GAAG;wCACxC;wCACA,eAAe,cAAc,KAAK,GAAG,KAAK,SAAS,aAAa;oCAClE;gCACF;;;gBACF;sDAAG,OAAO,yBAAyB;YAEnC;8CAAO,IAAM,cAAc;;QAC7B;qCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA7KgB"}}, {"offset": {"line": 5538, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5544, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Hyperliquid%20monitor/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { SubscriptionManager } from '@/components/SubscriptionManager';\nimport { PriceChart } from '@/components/PriceChart';\nimport { OrderBookDisplay } from '@/components/OrderBookDisplay';\nimport { WalletTracker } from '@/components/WalletTracker';\nimport { WalletAnalysis } from '@/components/WalletAnalysis';\nimport { LiveTradingFeed } from '@/components/LiveTradingFeed';\nimport { useHyperliquidWebSocket } from '@/hooks/useHyperliquidWebSocket';\nimport { useWalletTracker } from '@/hooks/useWalletTracker';\nimport { WalletData } from '@/types/hyperliquid';\nimport { Activity, TrendingUp, BookOpen, Settings, Wallet, Radio } from 'lucide-react';\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [selectedWallet, setSelectedWallet] = useState<WalletData | null>(null);\n\n  const {\n    connectionStatus,\n    subscriptions,\n    priceData,\n    orderBookData,\n    subscribe,\n    unsubscribe,\n    connect,\n    disconnect\n  } = useHyperliquidWebSocket();\n\n  const {\n    wallets,\n    walletTrades,\n    liveActivity,\n    addWallet,\n    removeWallet,\n    getWalletTrades,\n    calculateScalpingMetrics\n  } = useWalletTracker();\n\n  const handleWalletClick = (wallet: WalletData) => {\n    setSelectedWallet(wallet);\n    setActiveTab('wallet-analysis');\n  };\n\n  const handleBackToWallets = () => {\n    setSelectedWallet(null);\n    setActiveTab('wallets');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Activity className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                Hyperliquid Monitor\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className={`px-3 py-1 rounded-full text-sm font-medium ${\n                connectionStatus === 'connected'\n                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n                  : connectionStatus === 'connecting'\n                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n              }`}>\n                {connectionStatus}\n              </div>\n              <button\n                onClick={connectionStatus === 'connected' ? disconnect : connect}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                {connectionStatus === 'connected' ? 'Disconnect' : 'Connect'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className=\"bg-white dark:bg-gray-800 border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex space-x-8\">\n            {[\n              { id: 'overview', label: 'Overview', icon: TrendingUp },\n              { id: 'live-feed', label: 'Live Feed', icon: Radio },\n              { id: 'orderbook', label: 'Order Book', icon: BookOpen },\n              { id: 'wallets', label: 'Wallet Tracker', icon: Wallet },\n              { id: 'subscriptions', label: 'Subscriptions', icon: Settings },\n            ].map(({ id, label, icon: Icon }) => (\n              <button\n                key={id}\n                onClick={() => setActiveTab(id)}\n                className={`flex items-center px-3 py-4 text-sm font-medium border-b-2 transition-colors ${\n                  activeTab === id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <Icon className=\"h-4 w-4 mr-2\" />\n                {label}\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <PriceChart data={priceData} />\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n                  Active Subscriptions\n                </h3>\n                <div className=\"space-y-2\">\n                  {subscriptions.length === 0 ? (\n                    <p className=\"text-gray-500 dark:text-gray-400\">No active subscriptions</p>\n                  ) : (\n                    subscriptions.map((sub, index) => (\n                      <div key={index} className=\"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded\">\n                        <span className=\"font-medium text-gray-900 dark:text-white\">{sub.type}</span>\n                        <span className=\"text-sm text-gray-500 dark:text-gray-400\">{sub.symbol}</span>\n                      </div>\n                    ))\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'live-feed' && (\n          <LiveTradingFeed\n            wallets={wallets}\n            liveActivity={liveActivity}\n            recentTrades={walletTrades.slice(0, 20)}\n            onWalletClick={handleWalletClick}\n          />\n        )}\n\n        {activeTab === 'orderbook' && (\n          <OrderBookDisplay data={orderBookData} />\n        )}\n\n        {activeTab === 'wallets' && (\n          <WalletTracker\n            wallets={wallets}\n            walletTrades={walletTrades}\n            onAddWallet={addWallet}\n            onRemoveWallet={removeWallet}\n            onWalletClick={handleWalletClick}\n          />\n        )}\n\n        {activeTab === 'wallet-analysis' && selectedWallet && (\n          <WalletAnalysis\n            wallet={selectedWallet}\n            trades={getWalletTrades(selectedWallet.address)}\n            scalpingMetrics={calculateScalpingMetrics(selectedWallet.address)}\n            onBack={handleBackToWallets}\n          />\n        )}\n\n        {activeTab === 'subscriptions' && (\n          <SubscriptionManager\n            subscriptions={subscriptions}\n            onSubscribe={subscribe}\n            onUnsubscribe={unsubscribe}\n            connectionStatus={connectionStatus}\n          />\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAZA;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAExE,MAAM,EACJ,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,aAAa,EACb,SAAS,EACT,WAAW,EACX,OAAO,EACP,UAAU,EACX,GAAG,CAAA,GAAA,0IAAA,CAAA,0BAAuB,AAAD;IAE1B,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,eAAe,EACf,wBAAwB,EACzB,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,aAAa;IACf;IAEA,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;;;;;;;0CAIlE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,2CAA2C,EAC1D,qBAAqB,cACjB,sEACA,qBAAqB,eACrB,0EACA,6DACJ;kDACC;;;;;;kDAEH,6LAAC;wCACC,SAAS,qBAAqB,cAAc,aAAa;wCACzD,WAAU;kDAET,qBAAqB,cAAc,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,IAAI;gCAAY,OAAO;gCAAY,MAAM,qNAAA,CAAA,aAAU;4BAAC;4BACtD;gCAAE,IAAI;gCAAa,OAAO;gCAAa,MAAM,uMAAA,CAAA,QAAK;4BAAC;4BACnD;gCAAE,IAAI;gCAAa,OAAO;gCAAc,MAAM,iNAAA,CAAA,WAAQ;4BAAC;4BACvD;gCAAE,IAAI;gCAAW,OAAO;gCAAkB,MAAM,yMAAA,CAAA,SAAM;4BAAC;4BACvD;gCAAE,IAAI;gCAAiB,OAAO;gCAAiB,MAAM,6MAAA,CAAA,WAAQ;4BAAC;yBAC/D,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC9B,6LAAC;gCAEC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,KACV,qDACA,oGACJ;;kDAEF,6LAAC;wCAAK,WAAU;;;;;;oCACf;;+BATI;;;;;;;;;;;;;;;;;;;;0BAiBf,6LAAC;gBAAK,WAAU;;oBACb,cAAc,4BACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,aAAU;oCAAC,MAAM;;;;;;8CAClB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAGzE,6LAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;gDAAE,WAAU;0DAAmC;;;;;uDAEhD,cAAc,GAAG,CAAC,CAAC,KAAK,sBACtB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAK,WAAU;sEAA6C,IAAI,IAAI;;;;;;sEACrE,6LAAC;4DAAK,WAAU;sEAA4C,IAAI,MAAM;;;;;;;mDAF9D;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAYvB,cAAc,6BACb,6LAAC,wIAAA,CAAA,kBAAe;wBACd,SAAS;wBACT,cAAc;wBACd,cAAc,aAAa,KAAK,CAAC,GAAG;wBACpC,eAAe;;;;;;oBAIlB,cAAc,6BACb,6LAAC,yIAAA,CAAA,mBAAgB;wBAAC,MAAM;;;;;;oBAGzB,cAAc,2BACb,6LAAC,sIAAA,CAAA,gBAAa;wBACZ,SAAS;wBACT,cAAc;wBACd,aAAa;wBACb,gBAAgB;wBAChB,eAAe;;;;;;oBAIlB,cAAc,qBAAqB,gCAClC,6LAAC,uIAAA,CAAA,iBAAc;wBACb,QAAQ;wBACR,QAAQ,gBAAgB,eAAe,OAAO;wBAC9C,iBAAiB,yBAAyB,eAAe,OAAO;wBAChE,QAAQ;;;;;;oBAIX,cAAc,iCACb,6LAAC,4IAAA,CAAA,sBAAmB;wBAClB,eAAe;wBACf,aAAa;wBACb,eAAe;wBACf,kBAAkB;;;;;;;;;;;;;;;;;;AAM9B;GAtKwB;;QAalB,0IAAA,CAAA,0BAAuB;QAUvB,mIAAA,CAAA,mBAAgB;;;KAvBE"}}, {"offset": {"line": 5886, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}