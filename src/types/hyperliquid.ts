export interface HyperliquidSubscription {
  type: 'trades' | 'l2Book' | 'candle' | 'notification' | 'webData2' | 'activeAssetCtx';
  symbol?: string;
  coin?: string;
  interval?: string;
  user?: string;
}

export interface PriceData {
  symbol: string;
  price: number;
  timestamp: number;
  volume?: number;
  change24h?: number;
  high24h?: number;
  low24h?: number;
}

export interface OrderBookLevel {
  price: number;
  size: number;
}

export interface OrderBookData {
  symbol: string;
  bids: OrderBookLevel[];
  asks: OrderBookLevel[];
  timestamp: number;
}

export interface TradeData {
  symbol: string;
  price: number;
  size: number;
  side: 'buy' | 'sell';
  timestamp: number;
  tid: number;
}

export interface CandleData {
  symbol: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: number;
  interval: string;
}

export interface WebSocketMessage {
  channel: string;
  data: any;
}

export interface HyperliquidWebSocketResponse {
  channel: string;
  data: {
    trades?: TradeData[];
    levels?: [OrderBookLevel[], OrderBookLevel[]]; // [bids, asks]
    candle?: CandleData;
    coin?: string;
    ctx?: any;
  };
}

export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

export interface SubscriptionState {
  id: string;
  subscription: HyperliquidSubscription;
  active: boolean;
  lastUpdate?: number;
}

export interface MarketData {
  symbol: string;
  markPrice: number;
  indexPrice: number;
  fundingRate: number;
  openInterest: number;
  volume24h: number;
  change24h: number;
  high24h: number;
  low24h: number;
  lastUpdate: number;
}

export interface UserPosition {
  coin: string;
  szi: number; // size
  entryPx: number; // entry price
  positionValue: number;
  unrealizedPnl: number;
  returnOnEquity: number;
  leverage: number;
  maxLeverage: number;
  marginUsed: number;
}

export interface NotificationData {
  type: 'fill' | 'liquidation' | 'funding' | 'withdrawal' | 'deposit';
  message: string;
  timestamp: number;
  data?: any;
}

export interface WalletData {
  address: string;
  nickname?: string;
  totalVolume24h: number;
  totalPnl24h: number;
  totalPnlAllTime: number;
  tradeCount24h: number;
  avgTradeSize: number;
  maxTradeSize: number;
  minTradeSize: number;
  winRate: number;
  lastTradeTime: number;
  isScalper: boolean;
  scalpingScore: number; // 0-100, higher = more scalping behavior
  tags: string[];
}

export interface WalletTrade {
  walletAddress: string;
  symbol: string;
  side: 'buy' | 'sell';
  size: number;
  price: number;
  value: number; // size * price
  timestamp: number;
  pnl?: number;
  fee: number;
  tradeId: string;
}

export interface WalletFilter {
  minVolume24h?: number;
  maxVolume24h?: number;
  minPnl24h?: number;
  maxPnl24h?: number;
  minTradeSize?: number;
  maxTradeSize?: number;
  minWinRate?: number;
  maxWinRate?: number;
  onlyScalpers?: boolean;
  minScalpingScore?: number;
  tags?: string[];
  sortBy: 'volume24h' | 'pnl24h' | 'pnlAllTime' | 'winRate' | 'tradeCount' | 'scalpingScore';
  sortOrder: 'asc' | 'desc';
  limit: number;
}

export interface ScalpingMetrics {
  avgTimeBetweenTrades: number; // in minutes
  quickTradeRatio: number; // percentage of trades under 5 minutes
  smallTradeRatio: number; // percentage of trades under $1000
  reversalRate: number; // how often they reverse positions quickly
}
