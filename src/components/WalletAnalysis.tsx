'use client';

import { useState, useMemo } from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { WalletData, WalletTrade, ScalpingMetrics } from '@/types/hyperliquid';
import {
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  Zap,
  Activity,
  BarChart3,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  Clock,
  Brain,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface WalletAnalysisProps {
  wallet: WalletData;
  trades: WalletTrade[];
  scalpingMetrics: ScalpingMetrics;
  onBack: () => void;
}

export function WalletAnalysis({ wallet, trades, scalpingMetrics, onBack }: WalletAnalysisProps) {
  const [activeChart, setActiveChart] = useState<'pnl' | 'volume' | 'trades' | 'symbols'>('pnl');

  // Process data for charts
  const chartData = useMemo(() => {
    const sortedTrades = [...trades].sort((a, b) => a.timestamp - b.timestamp);
    let cumulativePnl = 0;
    let cumulativeVolume = 0;

    const timeSeriesData = sortedTrades.map((trade, index) => {
      cumulativePnl += trade.pnl || 0;
      cumulativeVolume += trade.value;

      return {
        timestamp: trade.timestamp,
        time: new Date(trade.timestamp).toLocaleTimeString(),
        date: new Date(trade.timestamp).toLocaleDateString(),
        cumulativePnl,
        cumulativeVolume,
        tradePnl: trade.pnl || 0,
        tradeValue: trade.value,
        tradeCount: index + 1,
        symbol: trade.symbol,
        side: trade.side
      };
    });

    return timeSeriesData;
  }, [trades]);

  const symbolAnalysis = useMemo(() => {
    const symbolMap = trades.reduce((acc, trade) => {
      if (!acc[trade.symbol]) {
        acc[trade.symbol] = {
          symbol: trade.symbol,
          totalTrades: 0,
          totalVolume: 0,
          totalPnl: 0,
          winningTrades: 0,
          losingTrades: 0,
          avgTradeSize: 0,
          winRate: 0,
          profitFactor: 0
        };
      }

      const symbolData = acc[trade.symbol];
      symbolData.totalTrades++;
      symbolData.totalVolume += trade.value;
      symbolData.totalPnl += trade.pnl || 0;

      if ((trade.pnl || 0) > 0) {
        symbolData.winningTrades++;
      } else if ((trade.pnl || 0) < 0) {
        symbolData.losingTrades++;
      }

      return acc;
    }, {} as Record<string, any>);

    return Object.values(symbolMap).map((item: any) => ({
      ...item,
      avgTradeSize: item.totalVolume / item.totalTrades,
      winRate: (item.winningTrades / item.totalTrades) * 100,
      profitFactor: item.totalPnl > 0 ?
        Math.abs(item.totalPnl) / Math.max(Math.abs(item.totalPnl - item.totalPnl), 1) : 0
    })).sort((a: any, b: any) => b.totalVolume - a.totalVolume);
  }, [trades]);

  const tradingPatterns = useMemo(() => {
    const hourlyActivity = new Array(24).fill(0);
    const dailyActivity = new Array(7).fill(0);

    trades.forEach(trade => {
      const date = new Date(trade.timestamp);
      const hour = date.getHours();
      const day = date.getDay();

      hourlyActivity[hour]++;
      dailyActivity[day]++;
    });

    return {
      hourlyActivity: hourlyActivity.map((count, hour) => ({
        hour: `${hour}:00`,
        trades: count,
        percentage: (count / trades.length) * 100
      })),
      dailyActivity: dailyActivity.map((count, day) => ({
        day: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][day],
        trades: count,
        percentage: (count / trades.length) * 100
      }))
    };
  }, [trades]);

  const strategyAnalysis = useMemo(() => {
    const analysis = {
      tradingStyle: 'Unknown',
      riskProfile: 'Medium',
      preferredSymbols: symbolAnalysis.slice(0, 3).map(s => s.symbol),
      avgHoldTime: 0,
      consistency: 0,
      momentum: 'Neutral',
      insights: [] as string[]
    };

    // Determine trading style
    if (wallet.isScalper && scalpingMetrics.avgTimeBetweenTrades < 10) {
      analysis.tradingStyle = 'High-Frequency Scalper';
      analysis.insights.push('Executes rapid trades with minimal hold times');
    } else if (wallet.avgTradeSize > 50000) {
      analysis.tradingStyle = 'Whale Trader';
      analysis.insights.push('Makes large position trades, likely institutional');
    } else if (wallet.winRate > 70) {
      analysis.tradingStyle = 'Precision Trader';
      analysis.insights.push('High win rate suggests careful entry/exit timing');
    } else {
      analysis.tradingStyle = 'Active Trader';
    }

    // Risk profile
    const maxTradeRatio = wallet.maxTradeSize / wallet.avgTradeSize;
    if (maxTradeRatio > 5) {
      analysis.riskProfile = 'High Risk';
      analysis.insights.push('Significant variation in trade sizes indicates high risk tolerance');
    } else if (wallet.winRate > 65) {
      analysis.riskProfile = 'Conservative';
      analysis.insights.push('Consistent win rate suggests risk-averse approach');
    }

    // Momentum
    const recentTrades = trades.slice(-10);
    const recentPnl = recentTrades.reduce((sum, trade) => sum + (trade.pnl || 0), 0);
    if (recentPnl > 0) {
      analysis.momentum = 'Bullish';
      analysis.insights.push('Recent trades show positive momentum');
    } else if (recentPnl < 0) {
      analysis.momentum = 'Bearish';
      analysis.insights.push('Recent performance shows negative trend');
    }

    // Consistency
    const dailyPnls = chartData.reduce((acc, point, index) => {
      const date = point.date;
      if (!acc[date]) acc[date] = 0;
      acc[date] += point.tradePnl;
      return acc;
    }, {} as Record<string, number>);

    const pnlValues = Object.values(dailyPnls);
    const positiveDays = pnlValues.filter(pnl => pnl > 0).length;
    analysis.consistency = (positiveDays / pnlValues.length) * 100;

    if (analysis.consistency > 70) {
      analysis.insights.push('Highly consistent daily performance');
    } else if (analysis.consistency < 40) {
      analysis.insights.push('Volatile daily performance with mixed results');
    }

    return analysis;
  }, [wallet, trades, scalpingMetrics, symbolAnalysis, chartData]);

  const formatCurrency = (value: number) => {
    if (Math.abs(value) >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (Math.abs(value) >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    } else {
      return `$${value.toFixed(2)}`;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 8)}...${address.slice(-6)}`;
  };

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onBack}
            className="flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Wallet List
          </button>

          <div className="flex items-center space-x-2">
            {wallet.isScalper && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                <Zap className="h-4 w-4 mr-1" />
                Scalper
              </span>
            )}
            {wallet.tags.map(tag => (
              <span key={tag} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {tag}
              </span>
            ))}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {wallet.nickname || 'Wallet Analysis'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 font-mono text-lg">
              {formatAddress(wallet.address)}
            </p>
          </div>

          <div className="text-right">
            <div className={`text-3xl font-bold ${
              wallet.totalPnl24h >= 0
                ? 'text-green-600 dark:text-green-400'
                : 'text-red-600 dark:text-red-400'
            }`}>
              {wallet.totalPnl24h >= 0 ? '+' : ''}{formatCurrency(wallet.totalPnl24h)}
            </div>
            <div className="text-gray-500 dark:text-gray-400">24h PnL</div>
          </div>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
          <DollarSign className="h-6 w-6 text-blue-500 mx-auto mb-2" />
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {formatCurrency(wallet.totalVolume24h)}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">24h Volume</div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
          <Target className="h-6 w-6 text-green-500 mx-auto mb-2" />
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {wallet.winRate.toFixed(1)}%
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Win Rate</div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
          <Activity className="h-6 w-6 text-purple-500 mx-auto mb-2" />
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {wallet.tradeCount24h}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Trades</div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
          <BarChart3 className="h-6 w-6 text-orange-500 mx-auto mb-2" />
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {formatCurrency(wallet.avgTradeSize)}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Avg Trade</div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
          <Clock className="h-6 w-6 text-indigo-500 mx-auto mb-2" />
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {scalpingMetrics.avgTimeBetweenTrades.toFixed(1)}m
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Avg Time</div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
          <Brain className="h-6 w-6 text-pink-500 mx-auto mb-2" />
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {strategyAnalysis.consistency.toFixed(0)}%
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Consistency</div>
        </div>
      </div>

      {/* Strategy Analysis */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
          <Brain className="h-5 w-5 mr-2 text-pink-500" />
          Strategy Analysis
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Trading Style</h4>
            <p className="text-blue-600 dark:text-blue-400 font-medium">{strategyAnalysis.tradingStyle}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Risk Profile: {strategyAnalysis.riskProfile}
            </p>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Preferred Assets</h4>
            <div className="flex flex-wrap gap-1">
              {strategyAnalysis.preferredSymbols.map(symbol => (
                <span key={symbol} className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-sm">
                  {symbol}
                </span>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Current Momentum</h4>
            <div className={`flex items-center ${
              strategyAnalysis.momentum === 'Bullish' ? 'text-green-600 dark:text-green-400' :
              strategyAnalysis.momentum === 'Bearish' ? 'text-red-600 dark:text-red-400' :
              'text-gray-600 dark:text-gray-400'
            }`}>
              {strategyAnalysis.momentum === 'Bullish' ? <TrendingUp className="h-4 w-4 mr-1" /> :
               strategyAnalysis.momentum === 'Bearish' ? <TrendingDown className="h-4 w-4 mr-1" /> :
               <Activity className="h-4 w-4 mr-1" />}
              {strategyAnalysis.momentum}
            </div>
          </div>
        </div>

        <div className="mt-4">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Key Insights</h4>
          <ul className="space-y-1">
            {strategyAnalysis.insights.map((insight, index) => (
              <li key={index} className="flex items-start text-sm text-gray-600 dark:text-gray-400">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                {insight}
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Chart Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Performance Charts
          </h3>

          <div className="flex space-x-2">
            {[
              { id: 'pnl', label: 'PnL Curve', icon: TrendingUp },
              { id: 'volume', label: 'Volume', icon: BarChart3 },
              { id: 'trades', label: 'Trade Activity', icon: Activity },
              { id: 'symbols', label: 'Asset Distribution', icon: PieChartIcon }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveChart(id as any)}
                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeChart === id
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                    : 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {label}
              </button>
            ))}
          </div>
        </div>

        <div className="h-80">
          {activeChart === 'pnl' && (
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis dataKey="time" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  formatter={(value: number) => [formatCurrency(value), 'Cumulative PnL']}
                  labelFormatter={(label) => `Time: ${label}`}
                />
                <Area
                  type="monotone"
                  dataKey="cumulativePnl"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.1}
                />
              </AreaChart>
            </ResponsiveContainer>
          )}

          {activeChart === 'volume' && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData.slice(-20)}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis dataKey="time" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  formatter={(value: number) => [formatCurrency(value), 'Trade Value']}
                />
                <Bar dataKey="tradeValue" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          )}

          {activeChart === 'trades' && (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={tradingPatterns.hourlyActivity}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis dataKey="hour" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="trades"
                  stroke="#F59E0B"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          )}

          {activeChart === 'symbols' && (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={symbolAnalysis}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  dataKey="totalVolume"
                  nameKey="symbol"
                  label={({ symbol, percent }) => `${symbol} ${(percent * 100).toFixed(0)}%`}
                >
                  {symbolAnalysis.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => [formatCurrency(value), 'Volume']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          )}
        </div>
      </div>

      {/* Detailed Trade History */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
          <Activity className="h-5 w-5 mr-2" />
          Complete Trade History ({trades.length} trades)
        </h3>

        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left py-3 text-gray-500 dark:text-gray-400">Time</th>
                <th className="text-left py-3 text-gray-500 dark:text-gray-400">Symbol</th>
                <th className="text-left py-3 text-gray-500 dark:text-gray-400">Side</th>
                <th className="text-right py-3 text-gray-500 dark:text-gray-400">Size</th>
                <th className="text-right py-3 text-gray-500 dark:text-gray-400">Price</th>
                <th className="text-right py-3 text-gray-500 dark:text-gray-400">Value</th>
                <th className="text-right py-3 text-gray-500 dark:text-gray-400">PnL</th>
                <th className="text-right py-3 text-gray-500 dark:text-gray-400">Fee</th>
              </tr>
            </thead>
            <tbody>
              {trades
                .sort((a, b) => b.timestamp - a.timestamp)
                .slice(0, 50)
                .map((trade) => (
                <tr key={trade.tradeId} className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="py-3 text-gray-600 dark:text-gray-400">
                    {new Date(trade.timestamp).toLocaleString()}
                  </td>
                  <td className="py-3 font-medium text-gray-900 dark:text-white">
                    {trade.symbol}
                  </td>
                  <td className="py-3">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      trade.side === 'buy'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {trade.side.toUpperCase()}
                    </span>
                  </td>
                  <td className="py-3 text-right font-mono text-gray-900 dark:text-white">
                    {trade.size.toFixed(4)}
                  </td>
                  <td className="py-3 text-right font-mono text-gray-900 dark:text-white">
                    ${trade.price.toFixed(2)}
                  </td>
                  <td className="py-3 text-right font-mono text-gray-900 dark:text-white">
                    {formatCurrency(trade.value)}
                  </td>
                  <td className={`py-3 text-right font-mono ${
                    (trade.pnl || 0) >= 0
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {(trade.pnl || 0) >= 0 ? '+' : ''}{formatCurrency(trade.pnl || 0)}
                  </td>
                  <td className="py-3 text-right font-mono text-gray-500 dark:text-gray-400">
                    {formatCurrency(trade.fee)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {trades.length > 50 && (
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Showing latest 50 trades of {trades.length} total
            </p>
          </div>
        )}
      </div>

      {/* Symbol Performance Breakdown */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          Performance by Symbol
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {symbolAnalysis.map((symbol: any) => (
            <div key={symbol.symbol} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900 dark:text-white">{symbol.symbol}</h4>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {symbol.totalTrades} trades
                </span>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Volume:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatCurrency(symbol.totalVolume)}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total PnL:</span>
                  <span className={`text-sm font-medium ${
                    symbol.totalPnl >= 0
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {symbol.totalPnl >= 0 ? '+' : ''}{formatCurrency(symbol.totalPnl)}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Win Rate:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {symbol.winRate.toFixed(1)}%
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Avg Trade:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatCurrency(symbol.avgTradeSize)}
                  </span>
                </div>

                {/* Win Rate Progress Bar */}
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                    <span>Win Rate</span>
                    <span>{symbol.winRate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        symbol.winRate >= 60 ? 'bg-green-500' :
                        symbol.winRate >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${Math.min(symbol.winRate, 100)}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
