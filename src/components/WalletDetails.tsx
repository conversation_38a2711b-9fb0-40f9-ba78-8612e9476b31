'use client';

import { useMemo } from 'react';
import { WalletData, WalletTrade, ScalpingMetrics } from '@/types/hyperliquid';
import { 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target,
  Zap,
  Activity,
  BarChart3
} from 'lucide-react';

interface WalletDetailsProps {
  wallet: WalletData;
  trades: WalletTrade[];
  scalpingMetrics: ScalpingMetrics;
}

export function WalletDetails({ wallet, trades, scalpingMetrics }: WalletDetailsProps) {
  const recentTrades = useMemo(() => {
    return trades
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10);
  }, [trades]);

  const tradesBySymbol = useMemo(() => {
    const symbolMap = trades.reduce((acc, trade) => {
      if (!acc[trade.symbol]) {
        acc[trade.symbol] = {
          symbol: trade.symbol,
          count: 0,
          totalVolume: 0,
          totalPnl: 0,
          avgSize: 0
        };
      }
      acc[trade.symbol].count++;
      acc[trade.symbol].totalVolume += trade.value;
      acc[trade.symbol].totalPnl += trade.pnl || 0;
      return acc;
    }, {} as Record<string, any>);

    return Object.values(symbolMap).map((item: any) => ({
      ...item,
      avgSize: item.totalVolume / item.count
    })).sort((a: any, b: any) => b.totalVolume - a.totalVolume);
  }, [trades]);

  const formatCurrency = (value: number) => {
    if (Math.abs(value) >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (Math.abs(value) >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    } else {
      return `$${value.toFixed(2)}`;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 8)}...${address.slice(-6)}`;
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="space-y-6">
      {/* Wallet Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {wallet.nickname || 'Wallet Details'}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 font-mono">
              {formatAddress(wallet.address)}
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            {wallet.isScalper && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                <Zap className="h-4 w-4 mr-1" />
                Scalper
              </span>
            )}
            {wallet.tags.map(tag => (
              <span key={tag} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {tag}
              </span>
            ))}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {formatCurrency(wallet.totalVolume24h)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">24h Volume</div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className={`text-2xl font-bold ${
              wallet.totalPnl24h >= 0 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-red-600 dark:text-red-400'
            }`}>
              {wallet.totalPnl24h >= 0 ? '+' : ''}{formatCurrency(wallet.totalPnl24h)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">24h PnL</div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {wallet.winRate.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Win Rate</div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {wallet.tradeCount24h}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Trades</div>
          </div>
        </div>
      </div>

      {/* Scalping Metrics */}
      {wallet.isScalper && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
            <Zap className="h-5 w-5 mr-2 text-yellow-500" />
            Scalping Analysis
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {scalpingMetrics.avgTimeBetweenTrades.toFixed(1)}m
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Avg Time Between</div>
            </div>
            
            <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {scalpingMetrics.quickTradeRatio.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Quick Trades</div>
            </div>
            
            <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {scalpingMetrics.smallTradeRatio.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Small Trades</div>
            </div>
            
            <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {scalpingMetrics.reversalRate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Reversal Rate</div>
            </div>
          </div>
        </div>
      )}

      {/* Trading by Symbol */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          Trading by Symbol
        </h3>
        
        <div className="space-y-3">
          {tradesBySymbol.map((item: any) => (
            <div key={item.symbol} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <span className="font-medium text-gray-900 dark:text-white">
                  {item.symbol}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {item.count} trades
                </span>
              </div>
              
              <div className="text-right">
                <div className="font-medium text-gray-900 dark:text-white">
                  {formatCurrency(item.totalVolume)}
                </div>
                <div className={`text-sm ${
                  item.totalPnl >= 0 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {item.totalPnl >= 0 ? '+' : ''}{formatCurrency(item.totalPnl)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Trades */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
          <Activity className="h-5 w-5 mr-2" />
          Recent Trades
        </h3>
        
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left py-2 text-gray-500 dark:text-gray-400">Time</th>
                <th className="text-left py-2 text-gray-500 dark:text-gray-400">Symbol</th>
                <th className="text-left py-2 text-gray-500 dark:text-gray-400">Side</th>
                <th className="text-right py-2 text-gray-500 dark:text-gray-400">Size</th>
                <th className="text-right py-2 text-gray-500 dark:text-gray-400">Price</th>
                <th className="text-right py-2 text-gray-500 dark:text-gray-400">Value</th>
                <th className="text-right py-2 text-gray-500 dark:text-gray-400">PnL</th>
              </tr>
            </thead>
            <tbody>
              {recentTrades.map((trade) => (
                <tr key={trade.tradeId} className="border-b border-gray-100 dark:border-gray-700">
                  <td className="py-2 text-gray-600 dark:text-gray-400">
                    {formatTimeAgo(trade.timestamp)}
                  </td>
                  <td className="py-2 font-medium text-gray-900 dark:text-white">
                    {trade.symbol}
                  </td>
                  <td className="py-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      trade.side === 'buy' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {trade.side.toUpperCase()}
                    </span>
                  </td>
                  <td className="py-2 text-right font-mono text-gray-900 dark:text-white">
                    {trade.size.toFixed(4)}
                  </td>
                  <td className="py-2 text-right font-mono text-gray-900 dark:text-white">
                    ${trade.price.toFixed(2)}
                  </td>
                  <td className="py-2 text-right font-mono text-gray-900 dark:text-white">
                    {formatCurrency(trade.value)}
                  </td>
                  <td className={`py-2 text-right font-mono ${
                    (trade.pnl || 0) >= 0 
                      ? 'text-green-600 dark:text-green-400' 
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {(trade.pnl || 0) >= 0 ? '+' : ''}{formatCurrency(trade.pnl || 0)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
