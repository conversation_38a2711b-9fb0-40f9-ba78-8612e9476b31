'use client';

import { useState } from 'react';
import { HyperliquidSubscription, ConnectionStatus } from '@/types/hyperliquid';
import { Plus, Trash2, Play, Pause } from 'lucide-react';

interface SubscriptionManagerProps {
  subscriptions: HyperliquidSubscription[];
  onSubscribe: (subscription: HyperliquidSubscription) => string;
  onUnsubscribe: (subscriptionId: string) => void;
  connectionStatus: ConnectionStatus;
}

const POPULAR_SYMBOLS = ['BTC', 'ETH', 'SOL', 'AVAX', 'MATIC', 'DOGE', 'ADA', 'DOT'];
const SUBSCRIPTION_TYPES = [
  { value: 'trades', label: 'Trades', description: 'Real-time trade data' },
  { value: 'l2Book', label: 'Order Book', description: 'Level 2 order book updates' },
  { value: 'candle', label: 'Candles', description: 'OHLCV candle data' },
  { value: 'webData2', label: 'Web Data', description: 'General market data' },
  { value: 'notification', label: 'Notifications', description: 'User notifications' },
  { value: 'activeAssetCtx', label: 'Asset Context', description: 'Active asset context' }
];

const CANDLE_INTERVALS = ['1m', '5m', '15m', '1h', '4h', '1d'];

export function SubscriptionManager({ 
  subscriptions, 
  onSubscribe, 
  onUnsubscribe, 
  connectionStatus 
}: SubscriptionManagerProps) {
  const [newSubscription, setNewSubscription] = useState<Partial<HyperliquidSubscription>>({
    type: 'trades',
    symbol: 'BTC'
  });
  const [showAddForm, setShowAddForm] = useState(false);

  const handleAddSubscription = () => {
    if (newSubscription.type) {
      const subscription: HyperliquidSubscription = {
        type: newSubscription.type,
        ...(newSubscription.symbol && { symbol: newSubscription.symbol }),
        ...(newSubscription.coin && { coin: newSubscription.coin }),
        ...(newSubscription.interval && { interval: newSubscription.interval }),
        ...(newSubscription.user && { user: newSubscription.user })
      };

      onSubscribe(subscription);
      setNewSubscription({ type: 'trades', symbol: 'BTC' });
      setShowAddForm(false);
    }
  };

  const isConnected = connectionStatus === 'connected';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Subscription Manager
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your Hyperliquid WebSocket subscriptions
          </p>
        </div>
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          disabled={!isConnected}
          className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
            isConnected
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Subscription
        </button>
      </div>

      {/* Connection Status Warning */}
      {!isConnected && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-center">
            <Pause className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
            <p className="text-yellow-800 dark:text-yellow-200">
              WebSocket is not connected. Connect to manage subscriptions.
            </p>
          </div>
        </div>
      )}

      {/* Add Subscription Form */}
      {showAddForm && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            Add New Subscription
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Subscription Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Subscription Type
              </label>
              <select
                value={newSubscription.type || ''}
                onChange={(e) => setNewSubscription(prev => ({ 
                  ...prev, 
                  type: e.target.value as HyperliquidSubscription['type']
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                {SUBSCRIPTION_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label} - {type.description}
                  </option>
                ))}
              </select>
            </div>

            {/* Symbol/Coin */}
            {(newSubscription.type === 'trades' || newSubscription.type === 'l2Book' || newSubscription.type === 'candle') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Symbol
                </label>
                <div className="flex space-x-2">
                  <select
                    value={newSubscription.symbol || ''}
                    onChange={(e) => setNewSubscription(prev => ({ ...prev, symbol: e.target.value }))}
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    {POPULAR_SYMBOLS.map(symbol => (
                      <option key={symbol} value={symbol}>{symbol}</option>
                    ))}
                  </select>
                  <input
                    type="text"
                    placeholder="Custom"
                    value={newSubscription.symbol || ''}
                    onChange={(e) => setNewSubscription(prev => ({ ...prev, symbol: e.target.value }))}
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>
            )}

            {/* Interval for candles */}
            {newSubscription.type === 'candle' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Interval
                </label>
                <select
                  value={newSubscription.interval || '1m'}
                  onChange={(e) => setNewSubscription(prev => ({ ...prev, interval: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  {CANDLE_INTERVALS.map(interval => (
                    <option key={interval} value={interval}>{interval}</option>
                  ))}
                </select>
              </div>
            )}

            {/* User for notifications */}
            {newSubscription.type === 'notification' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  User Address
                </label>
                <input
                  type="text"
                  placeholder="0x..."
                  value={newSubscription.user || ''}
                  onChange={(e) => setNewSubscription(prev => ({ ...prev, user: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleAddSubscription}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add Subscription
            </button>
          </div>
        </div>
      )}

      {/* Active Subscriptions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Active Subscriptions ({subscriptions.length})
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {subscriptions.length === 0 ? (
            <div className="px-6 py-8 text-center">
              <Play className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                No active subscriptions. Add one to get started.
              </p>
            </div>
          ) : (
            subscriptions.map((subscription, index) => (
              <div key={index} className="px-6 py-4 flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      {subscription.type}
                    </span>
                    {subscription.symbol && (
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {subscription.symbol}
                      </span>
                    )}
                    {subscription.coin && (
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {subscription.coin}
                      </span>
                    )}
                    {subscription.interval && (
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {subscription.interval}
                      </span>
                    )}
                    {subscription.user && (
                      <span className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                        {subscription.user.slice(0, 8)}...
                      </span>
                    )}
                  </div>
                </div>
                
                <button
                  onClick={() => onUnsubscribe(`sub_${index}`)}
                  disabled={!isConnected}
                  className={`p-2 rounded-lg transition-colors ${
                    isConnected
                      ? 'text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20'
                      : 'text-gray-400 cursor-not-allowed'
                  }`}
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
