'use client';

import { useMemo } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { PriceData } from '@/types/hyperliquid';
import { TrendingUp, TrendingDown, Activity } from 'lucide-react';

interface PriceChartProps {
  data: PriceData[];
}

export function PriceChart({ data }: PriceChartProps) {
  const chartData = useMemo(() => {
    // Group data by symbol and create time series
    const symbolData = data.reduce((acc, item) => {
      if (!acc[item.symbol]) {
        acc[item.symbol] = [];
      }
      acc[item.symbol].push({
        timestamp: item.timestamp,
        price: item.price,
        time: new Date(item.timestamp).toLocaleTimeString(),
        volume: item.volume || 0
      });
      return acc;
    }, {} as Record<string, any[]>);

    // Return the most recent data for the primary symbol (first one or BTC if available)
    const primarySymbol = data.find(d => d.symbol === 'BTC')?.symbol || data[0]?.symbol;
    if (!primarySymbol || !symbolData[primarySymbol]) return [];

    return symbolData[primarySymbol]
      .sort((a, b) => a.timestamp - b.timestamp)
      .slice(-50); // Keep last 50 data points
  }, [data]);

  const latestData = useMemo(() => {
    if (data.length === 0) return null;
    
    // Get latest data for each symbol
    const latest = data.reduce((acc, item) => {
      if (!acc[item.symbol] || item.timestamp > acc[item.symbol].timestamp) {
        acc[item.symbol] = item;
      }
      return acc;
    }, {} as Record<string, PriceData>);

    return Object.values(latest);
  }, [data]);

  const primarySymbol = latestData?.[0]?.symbol || 'N/A';
  const primaryPrice = latestData?.[0]?.price || 0;
  const priceChange = latestData?.[0]?.change24h || 0;

  if (!latestData || latestData.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              No price data available. Subscribe to trades or candles to see charts.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Price Chart
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Real-time price movements
          </p>
        </div>
        
        {/* Primary Symbol Info */}
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            ${primaryPrice.toLocaleString(undefined, { 
              minimumFractionDigits: 2, 
              maximumFractionDigits: 2 
            })}
          </div>
          <div className={`flex items-center text-sm ${
            priceChange >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {priceChange >= 0 ? (
              <TrendingUp className="h-4 w-4 mr-1" />
            ) : (
              <TrendingDown className="h-4 w-4 mr-1" />
            )}
            {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}%
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="h-64 mb-6">
        {chartData.length > 0 ? (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 12 }}
                className="text-gray-600 dark:text-gray-400"
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                className="text-gray-600 dark:text-gray-400"
                domain={['dataMin - 10', 'dataMax + 10']}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white'
                }}
                formatter={(value: number) => [
                  `$${value.toLocaleString(undefined, { 
                    minimumFractionDigits: 2, 
                    maximumFractionDigits: 2 
                  })}`, 
                  'Price'
                ]}
              />
              <Line 
                type="monotone" 
                dataKey="price" 
                stroke="#3B82F6" 
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 4, fill: '#3B82F6' }}
              />
            </LineChart>
          </ResponsiveContainer>
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500 dark:text-gray-400">
              Waiting for price data...
            </p>
          </div>
        )}
      </div>

      {/* Symbol Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {latestData.slice(0, 6).map((item) => (
          <div key={item.symbol} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {item.symbol}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {new Date(item.timestamp).toLocaleTimeString()}
              </span>
            </div>
            <div className="mt-1">
              <div className="text-lg font-semibold text-gray-900 dark:text-white">
                ${item.price.toLocaleString(undefined, { 
                  minimumFractionDigits: 2, 
                  maximumFractionDigits: 6 
                })}
              </div>
              {item.volume && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Vol: {item.volume.toLocaleString()}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
