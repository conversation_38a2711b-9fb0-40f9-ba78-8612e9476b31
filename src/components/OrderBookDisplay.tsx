'use client';

import { useMemo } from 'react';
import { OrderBookData, OrderBookLevel } from '@/types/hyperliquid';
import { BookOpen, TrendingUp, TrendingDown } from 'lucide-react';

interface OrderBookDisplayProps {
  data: OrderBookData | null;
}

interface ProcessedLevel extends OrderBookLevel {
  total: number;
  percentage: number;
}

export function OrderBookDisplay({ data }: OrderBookDisplayProps) {
  const processedData = useMemo(() => {
    if (!data) return null;

    // Process bids (buy orders) - sort by price descending
    const processedBids: ProcessedLevel[] = data.bids
      .sort((a, b) => b.price - a.price)
      .slice(0, 15)
      .reduce((acc, level, index) => {
        const total = acc.length > 0 ? acc[acc.length - 1].total + level.size : level.size;
        acc.push({
          ...level,
          total,
          percentage: 0 // Will be calculated after we have max total
        });
        return acc;
      }, [] as ProcessedLevel[]);

    // Process asks (sell orders) - sort by price ascending
    const processedAsks: ProcessedLevel[] = data.asks
      .sort((a, b) => a.price - b.price)
      .slice(0, 15)
      .reduce((acc, level, index) => {
        const total = acc.length > 0 ? acc[acc.length - 1].total + level.size : level.size;
        acc.push({
          ...level,
          total,
          percentage: 0 // Will be calculated after we have max total
        });
        return acc;
      }, [] as ProcessedLevel[]);

    // Calculate percentages
    const maxBidTotal = Math.max(...processedBids.map(b => b.total), 0);
    const maxAskTotal = Math.max(...processedAsks.map(a => a.total), 0);
    const maxTotal = Math.max(maxBidTotal, maxAskTotal);

    processedBids.forEach(bid => {
      bid.percentage = maxTotal > 0 ? (bid.total / maxTotal) * 100 : 0;
    });

    processedAsks.forEach(ask => {
      ask.percentage = maxTotal > 0 ? (ask.total / maxTotal) * 100 : 0;
    });

    // Calculate spread
    const bestBid = processedBids[0]?.price || 0;
    const bestAsk = processedAsks[0]?.price || 0;
    const spread = bestAsk - bestBid;
    const spreadPercentage = bestBid > 0 ? (spread / bestBid) * 100 : 0;

    return {
      bids: processedBids,
      asks: processedAsks.reverse(), // Reverse to show highest prices at top
      spread,
      spreadPercentage,
      bestBid,
      bestAsk
    };
  }, [data]);

  if (!data || !processedData) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              No order book data available. Subscribe to l2Book to see order book.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const { bids, asks, spread, spreadPercentage, bestBid, bestAsk } = processedData;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Order Book - {data.symbol}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Level 2 market depth
            </p>
          </div>
          
          {/* Spread Info */}
          <div className="text-right">
            <div className="text-sm text-gray-500 dark:text-gray-400">Spread</div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              ${spread.toFixed(2)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {spreadPercentage.toFixed(3)}%
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Column Headers */}
        <div className="grid grid-cols-3 gap-4 mb-4 text-sm font-medium text-gray-500 dark:text-gray-400">
          <div className="text-left">Price</div>
          <div className="text-center">Size</div>
          <div className="text-right">Total</div>
        </div>

        {/* Asks (Sell Orders) */}
        <div className="space-y-1 mb-4">
          {asks.map((ask, index) => (
            <div key={`ask-${index}`} className="relative">
              {/* Background bar */}
              <div 
                className="absolute inset-y-0 right-0 bg-red-100 dark:bg-red-900/20 rounded"
                style={{ width: `${ask.percentage}%` }}
              />
              
              {/* Content */}
              <div className="relative grid grid-cols-3 gap-4 py-1 px-2 text-sm">
                <div className="text-red-600 dark:text-red-400 font-mono">
                  {ask.price.toFixed(2)}
                </div>
                <div className="text-center text-gray-900 dark:text-white font-mono">
                  {ask.size.toFixed(4)}
                </div>
                <div className="text-right text-gray-600 dark:text-gray-400 font-mono">
                  {ask.total.toFixed(4)}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Spread Display */}
        <div className="flex items-center justify-center py-3 my-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center text-green-600 dark:text-green-400">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span className="font-mono">${bestBid.toFixed(2)}</span>
              </div>
              <div className="text-gray-400">|</div>
              <div className="flex items-center text-red-600 dark:text-red-400">
                <TrendingDown className="h-4 w-4 mr-1" />
                <span className="font-mono">${bestAsk.toFixed(2)}</span>
              </div>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Spread: ${spread.toFixed(2)} ({spreadPercentage.toFixed(3)}%)
            </div>
          </div>
        </div>

        {/* Bids (Buy Orders) */}
        <div className="space-y-1">
          {bids.map((bid, index) => (
            <div key={`bid-${index}`} className="relative">
              {/* Background bar */}
              <div 
                className="absolute inset-y-0 right-0 bg-green-100 dark:bg-green-900/20 rounded"
                style={{ width: `${bid.percentage}%` }}
              />
              
              {/* Content */}
              <div className="relative grid grid-cols-3 gap-4 py-1 px-2 text-sm">
                <div className="text-green-600 dark:text-green-400 font-mono">
                  {bid.price.toFixed(2)}
                </div>
                <div className="text-center text-gray-900 dark:text-white font-mono">
                  {bid.size.toFixed(4)}
                </div>
                <div className="text-right text-gray-600 dark:text-gray-400 font-mono">
                  {bid.total.toFixed(4)}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Footer Info */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
            <span>Last updated: {new Date(data.timestamp).toLocaleTimeString()}</span>
            <span>Showing top 15 levels</span>
          </div>
        </div>
      </div>
    </div>
  );
}
