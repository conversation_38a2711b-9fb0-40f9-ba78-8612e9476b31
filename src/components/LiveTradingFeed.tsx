'use client';

import { useState, useEffect } from 'react';
import { WalletData, WalletTrade, LiveTradingActivity, RealTimeUpdate } from '@/types/hyperliquid';
import { 
  Activity, 
  Zap, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  DollarSign,
  Target,
  AlertTriangle,
  Flame,
  Eye,
  Radio
} from 'lucide-react';

interface LiveTradingFeedProps {
  wallets: WalletData[];
  liveActivity: LiveTradingActivity[];
  recentTrades: WalletTrade[];
  onWalletClick: (wallet: WalletData) => void;
}

export function LiveTradingFeed({ 
  wallets, 
  liveActivity, 
  recentTrades, 
  onWalletClick 
}: LiveTradingFeedProps) {
  const [updates, setUpdates] = useState<RealTimeUpdate[]>([]);
  const [filter, setFilter] = useState<'all' | 'active' | 'hot' | 'high_volume'>('all');

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Generate random update
      const randomWallet = wallets[Math.floor(Math.random() * wallets.length)];
      const updateTypes = ['new_trade', 'position_change', 'pnl_update', 'activity_spike'] as const;
      const priorities = ['low', 'medium', 'high', 'urgent'] as const;
      
      const newUpdate: RealTimeUpdate = {
        type: updateTypes[Math.floor(Math.random() * updateTypes.length)],
        walletAddress: randomWallet.address,
        timestamp: Date.now(),
        data: {
          symbol: ['HYPER', 'BTC', 'ETH'][Math.floor(Math.random() * 3)],
          value: Math.random() * 50000 + 1000,
          side: Math.random() > 0.5 ? 'buy' : 'sell'
        },
        priority: priorities[Math.floor(Math.random() * priorities.length)]
      };

      setUpdates(prev => [newUpdate, ...prev.slice(0, 49)]); // Keep last 50 updates
    }, 2000 + Math.random() * 3000); // Random interval 2-5 seconds

    return () => clearInterval(interval);
  }, [wallets]);

  const filteredActivity = liveActivity.filter(activity => {
    switch (filter) {
      case 'active':
        return activity.isActiveNow;
      case 'hot':
        return activity.hotScore > 70;
      case 'high_volume':
        return activity.volumeInLastHour > 100000;
      default:
        return true;
    }
  }).sort((a, b) => b.hotScore - a.hotScore);

  const formatCurrency = (value: number) => {
    if (Math.abs(value) >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (Math.abs(value) >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    } else {
      return `$${value.toFixed(2)}`;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return `${seconds}s ago`;
  };

  const getWalletByAddress = (address: string) => {
    return wallets.find(w => w.address === address);
  };

  const getHotScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-500';
    if (score >= 60) return 'text-orange-500';
    if (score >= 40) return 'text-yellow-500';
    return 'text-gray-500';
  };

  const getUpdateIcon = (type: RealTimeUpdate['type']) => {
    switch (type) {
      case 'new_trade': return <Activity className="h-4 w-4" />;
      case 'position_change': return <TrendingUp className="h-4 w-4" />;
      case 'pnl_update': return <DollarSign className="h-4 w-4" />;
      case 'activity_spike': return <Zap className="h-4 w-4" />;
      default: return <Radio className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: RealTimeUpdate['priority']) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-50 dark:bg-red-900/20';
      case 'high': return 'text-orange-600 bg-orange-50 dark:bg-orange-900/20';
      case 'medium': return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20';
      default: return 'text-gray-600 bg-gray-50 dark:bg-gray-700';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Radio className="h-6 w-6 mr-2 text-red-500 animate-pulse" />
            Live Trading Feed
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Real-time wallet activity and trading updates
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/20 rounded-full">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
            <span className="text-sm text-green-700 dark:text-green-300">Live</span>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex space-x-4">
          {[
            { id: 'all', label: 'All Wallets', count: liveActivity.length },
            { id: 'active', label: 'Active Now', count: liveActivity.filter(a => a.isActiveNow).length },
            { id: 'hot', label: 'Hot Wallets', count: liveActivity.filter(a => a.hotScore > 70).length },
            { id: 'high_volume', label: 'High Volume', count: liveActivity.filter(a => a.volumeInLastHour > 100000).length }
          ].map(({ id, label, count }) => (
            <button
              key={id}
              onClick={() => setFilter(id as any)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                filter === id
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                  : 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
            >
              {label} ({count})
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Active Wallets */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Flame className="h-5 w-5 mr-2 text-orange-500" />
              Active Wallets ({filteredActivity.length})
            </h3>
          </div>
          
          <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto">
            {filteredActivity.map((activity) => {
              const wallet = getWalletByAddress(activity.walletAddress);
              if (!wallet) return null;

              return (
                <div 
                  key={activity.walletAddress} 
                  className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                  onClick={() => onWalletClick(wallet)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        {activity.isActiveNow && (
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        )}
                        <span className="font-mono text-sm text-gray-900 dark:text-white">
                          {formatAddress(activity.walletAddress)}
                        </span>
                        {wallet.nickname && (
                          <span className="text-sm text-blue-600 dark:text-blue-400">
                            ({wallet.nickname})
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {wallet.isScalper && (
                          <Zap className="h-4 w-4 text-yellow-500" title="Scalper" />
                        )}
                        <span className={`text-sm font-medium ${getHotScoreColor(activity.hotScore)}`}>
                          🔥 {activity.hotScore}
                        </span>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="flex items-center space-x-4 text-sm">
                        <div className="text-center">
                          <div className="font-medium text-gray-900 dark:text-white">
                            {activity.tradesInLast5Min}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">5m trades</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="font-medium text-gray-900 dark:text-white">
                            {formatCurrency(activity.volumeInLastHour)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">1h volume</div>
                        </div>
                        
                        <div className="text-center">
                          <div className={`font-medium ${
                            activity.pnlInLastHour >= 0 
                              ? 'text-green-600 dark:text-green-400' 
                              : 'text-red-600 dark:text-red-400'
                          }`}>
                            {activity.pnlInLastHour >= 0 ? '+' : ''}{formatCurrency(activity.pnlInLastHour)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">1h PnL</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-end space-x-2 mt-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          activity.momentum === 'bullish' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          activity.momentum === 'bearish' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}>
                          {activity.momentum}
                        </span>
                        
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          activity.riskLevel === 'extreme' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                          activity.riskLevel === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                          activity.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        }`}>
                          {activity.riskLevel} risk
                        </span>
                        
                        {activity.currentStreak > 0 && (
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            activity.streakType === 'winning' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                            'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          }`}>
                            {activity.currentStreak} {activity.streakType}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Real-time Updates Feed */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Activity className="h-5 w-5 mr-2 text-blue-500" />
              Live Updates
            </h3>
          </div>
          
          <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto">
            {updates.slice(0, 20).map((update, index) => {
              const wallet = getWalletByAddress(update.walletAddress);
              
              return (
                <div key={`${update.timestamp}-${index}`} className={`px-4 py-3 ${getPriorityColor(update.priority)}`}>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getUpdateIcon(update.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatAddress(update.walletAddress)}
                          {wallet?.nickname && (
                            <span className="text-xs text-gray-500 ml-1">
                              ({wallet.nickname})
                            </span>
                          )}
                        </p>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatTimeAgo(update.timestamp)}
                        </span>
                      </div>
                      
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {update.type === 'new_trade' && `${update.data.side.toUpperCase()} ${update.data.symbol} - ${formatCurrency(update.data.value)}`}
                        {update.type === 'position_change' && `Position updated for ${update.data.symbol}`}
                        {update.type === 'pnl_update' && `PnL changed: ${formatCurrency(update.data.value)}`}
                        {update.type === 'activity_spike' && `High activity detected - ${update.data.symbol}`}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
