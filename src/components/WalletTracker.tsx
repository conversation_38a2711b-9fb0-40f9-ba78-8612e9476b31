'use client';

import { useState, useMemo } from 'react';
import { WalletData, WalletFilter, WalletTrade } from '@/types/hyperliquid';
import {
  Wallet,
  Filter,
  TrendingUp,
  TrendingDown,
  Clock,
  DollarSign,
  Target,
  Zap,
  Star,
  Eye,
  EyeOff,
  Plus
} from 'lucide-react';

interface WalletTrackerProps {
  wallets: WalletData[];
  walletTrades: WalletTrade[];
  onAddWallet: (address: string, nickname?: string) => void;
  onRemoveWallet: (address: string) => void;
  onWalletClick: (wallet: WalletData) => void;
}

const DEFAULT_FILTER: WalletFilter = {
  sortBy: 'pnl24h',
  sortOrder: 'desc',
  limit: 10
};

export function WalletTracker({
  wallets,
  walletTrades,
  onAddWallet,
  onRemoveWallet,
  onWalletClick
}: WalletTrackerProps) {
  const [filter, setFilter] = useState<WalletFilter>(DEFAULT_FILTER);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newWalletAddress, setNewWalletAddress] = useState('');
  const [newWalletNickname, setNewWalletNickname] = useState('');
  const [watchedWallets, setWatchedWallets] = useState<Set<string>>(new Set());

  const filteredWallets = useMemo(() => {
    let filtered = wallets.filter(wallet => {
      if (filter.minVolume24h && wallet.totalVolume24h < filter.minVolume24h) return false;
      if (filter.maxVolume24h && wallet.totalVolume24h > filter.maxVolume24h) return false;
      if (filter.minPnl24h && wallet.totalPnl24h < filter.minPnl24h) return false;
      if (filter.maxPnl24h && wallet.totalPnl24h > filter.maxPnl24h) return false;
      if (filter.minTradeSize && wallet.avgTradeSize < filter.minTradeSize) return false;
      if (filter.maxTradeSize && wallet.maxTradeSize > filter.maxTradeSize) return false;
      if (filter.minWinRate && wallet.winRate < filter.minWinRate) return false;
      if (filter.maxWinRate && wallet.winRate > filter.maxWinRate) return false;
      if (filter.onlyScalpers && !wallet.isScalper) return false;
      if (filter.minScalpingScore && wallet.scalpingScore < filter.minScalpingScore) return false;
      if (filter.tags && filter.tags.length > 0) {
        const hasTag = filter.tags.some(tag => wallet.tags.includes(tag));
        if (!hasTag) return false;
      }
      return true;
    });

    // Sort
    filtered.sort((a, b) => {
      let aValue: number, bValue: number;
      switch (filter.sortBy) {
        case 'volume24h':
          aValue = a.totalVolume24h;
          bValue = b.totalVolume24h;
          break;
        case 'pnl24h':
          aValue = a.totalPnl24h;
          bValue = b.totalPnl24h;
          break;
        case 'pnlAllTime':
          aValue = a.totalPnlAllTime;
          bValue = b.totalPnlAllTime;
          break;
        case 'winRate':
          aValue = a.winRate;
          bValue = b.winRate;
          break;
        case 'tradeCount':
          aValue = a.tradeCount24h;
          bValue = b.tradeCount24h;
          break;
        case 'scalpingScore':
          aValue = a.scalpingScore;
          bValue = b.scalpingScore;
          break;
        default:
          aValue = a.totalPnl24h;
          bValue = b.totalPnl24h;
      }

      return filter.sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
    });

    return filtered.slice(0, filter.limit);
  }, [wallets, filter]);

  const handleAddWallet = () => {
    if (newWalletAddress.trim()) {
      onAddWallet(newWalletAddress.trim(), newWalletNickname.trim() || undefined);
      setNewWalletAddress('');
      setNewWalletNickname('');
      setShowAddForm(false);
    }
  };

  const toggleWatchWallet = (address: string) => {
    const newWatched = new Set(watchedWallets);
    if (newWatched.has(address)) {
      newWatched.delete(address);
    } else {
      newWatched.add(address);
    }
    setWatchedWallets(newWatched);
  };

  const formatCurrency = (value: number) => {
    if (Math.abs(value) >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (Math.abs(value) >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    } else {
      return `$${value.toFixed(2)}`;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Wallet Tracker
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Monitor top traders and scalpers on Hyperliquid
          </p>
        </div>
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Wallet
        </button>
      </div>

      {/* Add Wallet Form */}
      {showAddForm && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            Add Wallet to Track
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Wallet Address
              </label>
              <input
                type="text"
                placeholder="0x..."
                value={newWalletAddress}
                onChange={(e) => setNewWalletAddress(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Nickname (Optional)
              </label>
              <input
                type="text"
                placeholder="e.g., Whale #1"
                value={newWalletNickname}
                onChange={(e) => setNewWalletNickname(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleAddWallet}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add Wallet
            </button>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <Filter className="h-5 w-5 text-gray-500 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Filters</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Sort By */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Sort By
            </label>
            <select
              value={filter.sortBy}
              onChange={(e) => setFilter(prev => ({
                ...prev,
                sortBy: e.target.value as WalletFilter['sortBy']
              }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="pnl24h">24h PnL</option>
              <option value="pnlAllTime">All Time PnL</option>
              <option value="volume24h">24h Volume</option>
              <option value="winRate">Win Rate</option>
              <option value="tradeCount">Trade Count</option>
              <option value="scalpingScore">Scalping Score</option>
            </select>
          </div>

          {/* Limit */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Show Top
            </label>
            <select
              value={filter.limit}
              onChange={(e) => setFilter(prev => ({
                ...prev,
                limit: parseInt(e.target.value)
              }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value={5}>Top 5</option>
              <option value={10}>Top 10</option>
              <option value={25}>Top 25</option>
              <option value={50}>Top 50</option>
              <option value={100}>Top 100</option>
            </select>
          </div>

          {/* Max Trade Size */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Max Trade Size
            </label>
            <select
              value={filter.maxTradeSize || ''}
              onChange={(e) => setFilter(prev => ({
                ...prev,
                maxTradeSize: e.target.value ? parseInt(e.target.value) : undefined
              }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="">No Limit</option>
              <option value={1000}>$1K</option>
              <option value={5000}>$5K</option>
              <option value={10000}>$10K</option>
              <option value={50000}>$50K</option>
              <option value={100000}>$100K</option>
            </select>
          </div>

          {/* Scalpers Only */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Trader Type
            </label>
            <select
              value={filter.onlyScalpers ? 'scalpers' : 'all'}
              onChange={(e) => setFilter(prev => ({
                ...prev,
                onlyScalpers: e.target.value === 'scalpers'
              }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">All Traders</option>
              <option value="scalpers">Scalpers Only</option>
            </select>
          </div>
        </div>
      </div>

      {/* Wallet List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Top Wallets ({filteredWallets.length})
          </h3>
        </div>

        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredWallets.length === 0 ? (
            <div className="px-6 py-8 text-center">
              <Wallet className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                No wallets found matching your filters.
              </p>
            </div>
          ) : (
            filteredWallets.map((wallet, index) => (
              <div
                key={wallet.address}
                className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                onClick={() => onWalletClick(wallet)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        #{index + 1}
                      </span>
                      {wallet.isScalper && (
                        <Zap className="h-4 w-4 text-yellow-500" title="Scalper" />
                      )}
                    </div>

                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-mono text-sm text-gray-900 dark:text-white">
                          {formatAddress(wallet.address)}
                        </span>
                        {wallet.nickname && (
                          <span className="text-sm text-blue-600 dark:text-blue-400">
                            ({wallet.nickname})
                          </span>
                        )}
                      </div>

                      <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                        <span className="flex items-center">
                          <DollarSign className="h-3 w-3 mr-1" />
                          Vol: {formatCurrency(wallet.totalVolume24h)}
                        </span>
                        <span className="flex items-center">
                          <Target className="h-3 w-3 mr-1" />
                          Win: {wallet.winRate.toFixed(1)}%
                        </span>
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {wallet.tradeCount24h} trades
                        </span>
                        {wallet.isScalper && (
                          <span className="flex items-center text-yellow-600 dark:text-yellow-400">
                            <Zap className="h-3 w-3 mr-1" />
                            Score: {wallet.scalpingScore}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className={`text-lg font-semibold ${
                        wallet.totalPnl24h >= 0
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {wallet.totalPnl24h >= 0 ? '+' : ''}{formatCurrency(wallet.totalPnl24h)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        24h PnL
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={() => toggleWatchWallet(wallet.address)}
                        className={`p-2 rounded-lg transition-colors ${
                          watchedWallets.has(wallet.address)
                            ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                            : 'text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                        }`}
                        title={watchedWallets.has(wallet.address) ? 'Stop watching' : 'Watch wallet'}
                      >
                        {watchedWallets.has(wallet.address) ? (
                          <Eye className="h-4 w-4" />
                        ) : (
                          <EyeOff className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
