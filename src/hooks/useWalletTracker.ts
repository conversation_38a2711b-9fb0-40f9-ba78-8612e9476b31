'use client';

import { useState, useEffect, useCallback } from 'react';
import { WalletData, WalletTrade, ScalpingMetrics, LiveTradingActivity, RealTimeUpdate } from '@/types/hyperliquid';

// Mock data generator for demonstration
const generateMockWalletData = (): WalletData[] => {
  const addresses = [
    '******************************************',
    '0x8ba1f109551bD432803012645Hac136c22C501e',
    '******************************************',
    '******************************************',
    '******************************************',
    '0xfedcbafedcbafedcbafedcbafedcbafedcbafedcba',
    '******************************************',
    '******************************************',
    '******************************************',
    '******************************************',
    '******************************************',
    '******************************************',
    '******************************************',
    '******************************************',
    '******************************************',
  ];

  const nicknames = [
    'Whale Hunter', 'Scalp Master', 'Quick Trader', 'Volume King',
    'Profit Wizard', 'Speed Demon', 'Market Maker', 'Trend Rider',
    'Risk Taker', 'Smart Money', 'Flash Trader', 'Momentum Player',
    'Arbitrage Pro', 'Swing Master', 'Day Trader'
  ];

  return addresses.map((address, index) => {
    const isScalper = Math.random() > 0.6; // 40% chance of being a scalper
    const volume24h = Math.random() * 5000000 + 10000; // $10K to $5M
    const pnl24h = (Math.random() - 0.3) * volume24h * 0.1; // -30% to +70% of 10% volume
    const pnlAllTime = pnl24h * (Math.random() * 100 + 10); // 10-110 days worth
    const tradeCount = isScalper ?
      Math.floor(Math.random() * 200 + 50) : // Scalpers: 50-250 trades
      Math.floor(Math.random() * 50 + 5);    // Others: 5-55 trades

    const avgTradeSize = volume24h / tradeCount;
    const maxTradeSize = avgTradeSize * (Math.random() * 5 + 2); // 2-7x avg
    const minTradeSize = avgTradeSize * (Math.random() * 0.5 + 0.1); // 0.1-0.6x avg

    const winRate = Math.random() * 40 + 40; // 40-80% win rate
    const scalpingScore = isScalper ?
      Math.floor(Math.random() * 40 + 60) : // Scalpers: 60-100
      Math.floor(Math.random() * 60 + 10);   // Others: 10-70

    const tags = [];
    if (isScalper) tags.push('scalper');
    if (volume24h > 1000000) tags.push('whale');
    if (pnl24h > 50000) tags.push('profitable');
    if (tradeCount > 100) tags.push('active');
    if (winRate > 70) tags.push('skilled');

    return {
      address,
      nickname: Math.random() > 0.3 ? nicknames[index % nicknames.length] : undefined,
      totalVolume24h: volume24h,
      totalPnl24h: pnl24h,
      totalPnlAllTime: pnlAllTime,
      tradeCount24h: tradeCount,
      avgTradeSize,
      maxTradeSize,
      minTradeSize,
      winRate,
      lastTradeTime: Date.now() - Math.random() * 3600000, // Within last hour
      isScalper,
      scalpingScore,
      tags
    };
  });
};

const generateMockTrades = (wallets: WalletData[]): WalletTrade[] => {
  const trades: WalletTrade[] = [];
  const symbols = ['HYPER', 'BTC', 'ETH', 'SOL', 'AVAX'];
  const orderTypes = ['market', 'limit', 'stop', 'stop_limit', 'take_profit', 'trailing_stop'] as const;
  const timeInForceOptions = ['GTC', 'IOC', 'FOK', 'GTD'] as const;

  wallets.forEach(wallet => {
    const tradeCount = Math.min(wallet.tradeCount24h, 20); // Show last 20 trades max
    let currentPosition: Record<string, number> = {}; // Track positions per symbol

    for (let i = 0; i < tradeCount; i++) {
      const symbol = symbols[Math.floor(Math.random() * symbols.length)];
      const side = Math.random() > 0.5 ? 'buy' : 'sell';
      const size = Math.random() * 100 + 1;
      const price = symbol === 'HYPER' ? 28.5 + (Math.random() - 0.5) * 2 :
                   symbol === 'BTC' ? 45000 + (Math.random() - 0.5) * 1000 :
                   symbol === 'ETH' ? 3200 + (Math.random() - 0.5) * 200 :
                   symbol === 'SOL' ? 100 + (Math.random() - 0.5) * 10 :
                   50 + (Math.random() - 0.5) * 5;

      const value = size * price;
      const pnl = (Math.random() - 0.4) * value * 0.05; // -40% to +60% of 5% value
      const fee = value * 0.001; // 0.1% fee

      // Determine position side and changes
      const currentPos = currentPosition[symbol] || 0;
      const positionChange = side === 'buy' ? size : -size;
      const newPosition = currentPos + positionChange;
      currentPosition[symbol] = newPosition;

      const isClosingPosition = (currentPos > 0 && side === 'sell') || (currentPos < 0 && side === 'buy');
      const positionSide = newPosition > 0 ? 'long' : newPosition < 0 ? 'short' : 'neutral';

      // Generate order details
      const orderType = orderTypes[Math.floor(Math.random() * orderTypes.length)];
      const isScalper = wallet.isScalper;
      const hasStopLoss = Math.random() > (isScalper ? 0.7 : 0.4); // Scalpers use less stop losses
      const hasTakeProfit = Math.random() > (isScalper ? 0.5 : 0.3);
      const leverage = Math.random() > 0.6 ? Math.floor(Math.random() * 10) + 1 : undefined;

      const stopLoss = hasStopLoss ?
        (side === 'buy' ? price * (0.95 - Math.random() * 0.05) : price * (1.05 + Math.random() * 0.05)) :
        undefined;

      const takeProfit = hasTakeProfit ?
        (side === 'buy' ? price * (1.02 + Math.random() * 0.08) : price * (0.98 - Math.random() * 0.08)) :
        undefined;

      const riskRewardRatio = (hasStopLoss && hasTakeProfit) ?
        Math.abs((takeProfit! - price) / (stopLoss! - price)) : undefined;

      const executionType = orderType === 'market' ? 'taker' :
                           (Math.random() > 0.6 ? 'maker' : 'taker');

      const positionSizing = value > 50000 ? 'aggressive' :
                            value > 10000 ? 'moderate' : 'conservative';

      trades.push({
        walletAddress: wallet.address,
        symbol,
        side,
        size,
        price,
        value,
        timestamp: Date.now() - Math.random() * 86400000, // Within last 24h
        pnl,
        fee,
        tradeId: `trade_${wallet.address}_${i}`,
        orderDetails: {
          orderType,
          positionSide,
          leverage,
          stopLoss,
          takeProfit,
          trailingStopDistance: orderType === 'trailing_stop' ? Math.random() * 2 + 0.5 : undefined,
          timeInForce: timeInForceOptions[Math.floor(Math.random() * timeInForceOptions.length)],
          reduceOnly: isClosingPosition && Math.random() > 0.5,
          postOnly: orderType === 'limit' && Math.random() > 0.7,
          triggerPrice: ['stop', 'stop_limit', 'take_profit'].includes(orderType) ?
            price * (0.98 + Math.random() * 0.04) : undefined,
          executionType,
          slippage: executionType === 'taker' ? Math.random() * 0.002 : undefined,
          partialFills: Math.floor(Math.random() * 3),
          averageFillPrice: price * (0.999 + Math.random() * 0.002)
        },
        riskManagement: {
          hasStopLoss,
          hasTakeProfit,
          riskRewardRatio,
          maxDrawdown: Math.random() * 0.15, // 0-15% max drawdown
          positionSizing,
          hedged: Math.random() > 0.8, // 20% chance of being hedged
          marginUsed: leverage ? value / leverage : value,
          liquidationPrice: leverage ?
            (side === 'buy' ? price * (1 - 0.8/leverage) : price * (1 + 0.8/leverage)) :
            undefined
        },
        relatedOrders: hasStopLoss || hasTakeProfit ?
          [`sl_${wallet.address}_${i}`, `tp_${wallet.address}_${i}`].filter((_, idx) =>
            (idx === 0 && hasStopLoss) || (idx === 1 && hasTakeProfit)
          ) : undefined,
        isClosingPosition,
        positionChange
      });
    }
  });

  return trades.sort((a, b) => b.timestamp - a.timestamp);
};

const generateLiveActivity = (wallets: WalletData[]): LiveTradingActivity[] => {
  return wallets.map(wallet => {
    const now = Date.now();
    const lastTradeTime = now - Math.random() * 3600000; // Within last hour
    const isActiveNow = (now - lastTradeTime) < 300000; // Active if traded in last 5 minutes

    const tradesInLastHour = Math.floor(Math.random() * 20);
    const tradesInLast5Min = isActiveNow ? Math.floor(Math.random() * 5) : 0;
    const volumeInLastHour = tradesInLastHour * (Math.random() * 10000 + 1000);
    const pnlInLastHour = (Math.random() - 0.4) * volumeInLastHour * 0.05;

    const currentStreak = Math.floor(Math.random() * 8);
    const streakType = currentStreak === 0 ? 'neutral' :
                      Math.random() > 0.5 ? 'winning' : 'losing';

    // Calculate hot score based on activity
    let hotScore = 0;
    if (isActiveNow) hotScore += 30;
    if (tradesInLastHour > 10) hotScore += 25;
    if (volumeInLastHour > 50000) hotScore += 20;
    if (pnlInLastHour > 0) hotScore += 15;
    if (wallet.isScalper) hotScore += 10;
    hotScore = Math.min(100, hotScore + Math.random() * 20);

    const momentum = pnlInLastHour > 1000 ? 'bullish' :
                    pnlInLastHour < -1000 ? 'bearish' : 'neutral';

    const riskLevel = volumeInLastHour > 100000 ? 'extreme' :
                     volumeInLastHour > 50000 ? 'high' :
                     volumeInLastHour > 20000 ? 'medium' : 'low';

    return {
      walletAddress: wallet.address,
      isActiveNow,
      lastTradeTime,
      tradesInLastHour,
      tradesInLast5Min,
      volumeInLastHour,
      pnlInLastHour,
      currentStreak,
      streakType,
      hotScore,
      momentum,
      riskLevel
    };
  });
};

export function useWalletTracker() {
  const [wallets, setWallets] = useState<WalletData[]>([]);
  const [walletTrades, setWalletTrades] = useState<WalletTrade[]>([]);
  const [liveActivity, setLiveActivity] = useState<LiveTradingActivity[]>([]);
  const [trackedWallets, setTrackedWallets] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);

  // Initialize with mock data
  useEffect(() => {
    const mockWallets = generateMockWalletData();
    const mockTrades = generateMockTrades(mockWallets);
    const mockLiveActivity = generateLiveActivity(mockWallets);

    setWallets(mockWallets);
    setWalletTrades(mockTrades);
    setLiveActivity(mockLiveActivity);
  }, []);

  const addWallet = useCallback((address: string, nickname?: string) => {
    if (trackedWallets.has(address)) {
      console.log('Wallet already being tracked');
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      // Generate mock data for new wallet
      const volume24h = Math.random() * 1000000 + 5000;
      const pnl24h = (Math.random() - 0.3) * volume24h * 0.08;
      const isScalper = Math.random() > 0.7;
      const tradeCount = isScalper ?
        Math.floor(Math.random() * 150 + 30) :
        Math.floor(Math.random() * 30 + 3);

      const newWallet: WalletData = {
        address,
        nickname,
        totalVolume24h: volume24h,
        totalPnl24h: pnl24h,
        totalPnlAllTime: pnl24h * (Math.random() * 50 + 5),
        tradeCount24h: tradeCount,
        avgTradeSize: volume24h / tradeCount,
        maxTradeSize: volume24h / tradeCount * (Math.random() * 4 + 2),
        minTradeSize: volume24h / tradeCount * (Math.random() * 0.4 + 0.1),
        winRate: Math.random() * 35 + 45,
        lastTradeTime: Date.now() - Math.random() * 1800000,
        isScalper,
        scalpingScore: isScalper ? Math.floor(Math.random() * 35 + 65) : Math.floor(Math.random() * 50 + 15),
        tags: isScalper ? ['scalper', 'tracked'] : ['tracked']
      };

      setWallets(prev => [...prev, newWallet]);
      setTrackedWallets(prev => new Set([...prev, address]));
      setIsLoading(false);
    }, 1000);
  }, [trackedWallets]);

  const removeWallet = useCallback((address: string) => {
    setWallets(prev => prev.filter(w => w.address !== address));
    setTrackedWallets(prev => {
      const newSet = new Set(prev);
      newSet.delete(address);
      return newSet;
    });
    setWalletTrades(prev => prev.filter(t => t.walletAddress !== address));
  }, []);

  const getWalletTrades = useCallback((address: string): WalletTrade[] => {
    return walletTrades.filter(trade => trade.walletAddress === address);
  }, [walletTrades]);

  const calculateScalpingMetrics = useCallback((address: string): ScalpingMetrics => {
    const trades = getWalletTrades(address).sort((a, b) => a.timestamp - b.timestamp);

    if (trades.length < 2) {
      return {
        avgTimeBetweenTrades: 0,
        quickTradeRatio: 0,
        smallTradeRatio: 0,
        reversalRate: 0
      };
    }

    // Calculate average time between trades
    const timeDiffs = [];
    for (let i = 1; i < trades.length; i++) {
      timeDiffs.push((trades[i].timestamp - trades[i-1].timestamp) / (1000 * 60)); // minutes
    }
    const avgTimeBetweenTrades = timeDiffs.reduce((a, b) => a + b, 0) / timeDiffs.length;

    // Quick trades (under 5 minutes)
    const quickTrades = timeDiffs.filter(diff => diff < 5).length;
    const quickTradeRatio = (quickTrades / timeDiffs.length) * 100;

    // Small trades (under $1000)
    const smallTrades = trades.filter(trade => trade.value < 1000).length;
    const smallTradeRatio = (smallTrades / trades.length) * 100;

    // Reversal rate (buy followed by sell or vice versa within 10 minutes)
    let reversals = 0;
    for (let i = 1; i < trades.length; i++) {
      const timeDiff = (trades[i].timestamp - trades[i-1].timestamp) / (1000 * 60);
      if (timeDiff < 10 && trades[i].side !== trades[i-1].side) {
        reversals++;
      }
    }
    const reversalRate = (reversals / (trades.length - 1)) * 100;

    return {
      avgTimeBetweenTrades,
      quickTradeRatio,
      smallTradeRatio,
      reversalRate
    };
  }, [getWalletTrades]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setWallets(prev => prev.map(wallet => {
        // Small random updates to simulate real-time data
        const volumeChange = (Math.random() - 0.5) * wallet.totalVolume24h * 0.01;
        const pnlChange = (Math.random() - 0.5) * Math.abs(wallet.totalPnl24h) * 0.05;

        return {
          ...wallet,
          totalVolume24h: Math.max(0, wallet.totalVolume24h + volumeChange),
          totalPnl24h: wallet.totalPnl24h + pnlChange,
          lastTradeTime: Math.random() > 0.9 ? Date.now() : wallet.lastTradeTime
        };
      }));

      // Update live activity
      setLiveActivity(prev => prev.map(activity => {
        const volumeChange = (Math.random() - 0.5) * activity.volumeInLastHour * 0.1;
        const pnlChange = (Math.random() - 0.5) * Math.abs(activity.pnlInLastHour) * 0.1;
        const newTradesInLast5Min = Math.random() > 0.8 ? Math.floor(Math.random() * 3) : activity.tradesInLast5Min;
        const isActiveNow = newTradesInLast5Min > 0 || Math.random() > 0.85;

        // Recalculate hot score
        let hotScore = activity.hotScore;
        if (isActiveNow && !activity.isActiveNow) hotScore += 20;
        if (!isActiveNow && activity.isActiveNow) hotScore -= 15;
        hotScore = Math.max(0, Math.min(100, hotScore + (Math.random() - 0.5) * 10));

        return {
          ...activity,
          isActiveNow,
          tradesInLast5Min: newTradesInLast5Min,
          volumeInLastHour: Math.max(0, activity.volumeInLastHour + volumeChange),
          pnlInLastHour: activity.pnlInLastHour + pnlChange,
          hotScore,
          lastTradeTime: isActiveNow ? Date.now() : activity.lastTradeTime
        };
      }));
    }, 3000); // Update every 3 seconds

    return () => clearInterval(interval);
  }, []);

  return {
    wallets,
    walletTrades,
    liveActivity,
    trackedWallets,
    isLoading,
    addWallet,
    removeWallet,
    getWalletTrades,
    calculateScalpingMetrics
  };
}
