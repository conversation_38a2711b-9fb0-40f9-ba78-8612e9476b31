'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import {
  HyperliquidSubscription,
  PriceData,
  OrderBookData,
  TradeData,
  CandleData,
  ConnectionStatus,
  SubscriptionState,
  HyperliquidWebSocketResponse,
  OrderBookLevel
} from '@/types/hyperliquid';

const HYPERLIQUID_WS_URL = 'wss://api.hyperliquid.xyz/ws';

export function useHyperliquidWebSocket() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [subscriptions, setSubscriptions] = useState<SubscriptionState[]>([]);
  const [priceData, setPriceData] = useState<PriceData[]>([
    // Mock data for demonstration
    {
      symbol: 'BTC',
      price: 45000,
      timestamp: Date.now(),
      volume: 1234567,
      change24h: 2.5,
      high24h: 46000,
      low24h: 44000
    },
    {
      symbol: 'ETH',
      price: 3200,
      timestamp: Date.now(),
      volume: 987654,
      change24h: -1.2,
      high24h: 3300,
      low24h: 3100
    }
  ]);
  const [orderBookData, setOrderBookData] = useState<OrderBookData | null>({
    symbol: 'BTC',
    bids: [
      { price: 44995, size: 0.5 },
      { price: 44990, size: 1.2 },
      { price: 44985, size: 0.8 },
      { price: 44980, size: 2.1 },
      { price: 44975, size: 1.5 }
    ],
    asks: [
      { price: 45005, size: 0.7 },
      { price: 45010, size: 1.1 },
      { price: 45015, size: 0.9 },
      { price: 45020, size: 1.8 },
      { price: 45025, size: 1.3 }
    ],
    timestamp: Date.now()
  });
  const [tradeData, setTradeData] = useState<TradeData[]>([]);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const subscriptionIdCounter = useRef(0);

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionStatus('connecting');

    try {
      // Check if WebSocket is available in the browser
      if (typeof WebSocket === 'undefined') {
        console.error('WebSocket is not supported in this environment');
        setConnectionStatus('error');
        return;
      }

      const ws = new WebSocket(HYPERLIQUID_WS_URL);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected to Hyperliquid');
        setConnectionStatus('connected');

        // Resubscribe to active subscriptions
        subscriptions.forEach(sub => {
          if (sub.active) {
            try {
              ws.send(JSON.stringify({
                method: 'subscribe',
                subscription: sub.subscription
              }));
            } catch (error) {
              console.error('Error sending subscription:', error);
            }
          }
        });
      };

      ws.onmessage = (event) => {
        try {
          const message: HyperliquidWebSocketResponse = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setConnectionStatus('disconnected');
        wsRef.current = null;

        // Auto-reconnect after 3 seconds if not manually disconnected
        if (event.code !== 1000 && event.code !== 1001) {
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log('Attempting to reconnect...');
            connect();
          }, 3000);
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionStatus('error');
    }
  }, [subscriptions]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }
    setConnectionStatus('disconnected');
  }, []);

  const handleWebSocketMessage = useCallback((message: HyperliquidWebSocketResponse) => {
    const { channel, data } = message;

    switch (channel) {
      case 'trades':
        if (data.trades) {
          setTradeData(prev => [...prev.slice(-99), ...data.trades!].slice(-100));

          // Update price data from trades
          data.trades.forEach(trade => {
            setPriceData(prev => {
              const existing = prev.find(p => p.symbol === trade.symbol);
              if (existing) {
                return prev.map(p =>
                  p.symbol === trade.symbol
                    ? { ...p, price: trade.price, timestamp: trade.timestamp }
                    : p
                );
              } else {
                return [...prev, {
                  symbol: trade.symbol,
                  price: trade.price,
                  timestamp: trade.timestamp
                }];
              }
            });
          });
        }
        break;

      case 'l2Book':
        if (data.levels && data.coin) {
          const [bids, asks] = data.levels;
          setOrderBookData({
            symbol: data.coin,
            bids: bids || [],
            asks: asks || [],
            timestamp: Date.now()
          });
        }
        break;

      case 'candle':
        if (data.candle) {
          setPriceData(prev => {
            const existing = prev.find(p => p.symbol === data.candle!.symbol);
            if (existing) {
              return prev.map(p =>
                p.symbol === data.candle!.symbol
                  ? {
                      ...p,
                      price: data.candle!.close,
                      timestamp: data.candle!.timestamp,
                      volume: data.candle!.volume,
                      high24h: data.candle!.high,
                      low24h: data.candle!.low
                    }
                  : p
              );
            } else {
              return [...prev, {
                symbol: data.candle!.symbol,
                price: data.candle!.close,
                timestamp: data.candle!.timestamp,
                volume: data.candle!.volume,
                high24h: data.candle!.high,
                low24h: data.candle!.low
              }];
            }
          });
        }
        break;

      default:
        console.log('Unhandled channel:', channel, data);
    }
  }, []);

  const subscribe = useCallback((subscription: HyperliquidSubscription) => {
    const id = `sub_${subscriptionIdCounter.current++}`;

    const newSubscription: SubscriptionState = {
      id,
      subscription,
      active: true,
      lastUpdate: Date.now()
    };

    setSubscriptions(prev => [...prev, newSubscription]);

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.send(JSON.stringify({
          method: 'subscribe',
          subscription
        }));
        console.log('Subscribed to:', subscription);
      } catch (error) {
        console.error('Error sending subscription:', error);
      }
    } else {
      console.log('WebSocket not connected, subscription queued:', subscription);
    }

    return id;
  }, []);

  const unsubscribe = useCallback((subscriptionId: string) => {
    setSubscriptions(prev => {
      const subscription = prev.find(sub => sub.id === subscriptionId);
      if (subscription && wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({
          method: 'unsubscribe',
          subscription: subscription.subscription
        }));
      }
      return prev.filter(sub => sub.id !== subscriptionId);
    });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    connectionStatus,
    subscriptions: subscriptions.map(sub => sub.subscription),
    priceData,
    orderBookData,
    tradeData,
    connect,
    disconnect,
    subscribe,
    unsubscribe
  };
}
