'use client';

import { useState, useEffect } from 'react';
import { SubscriptionManager } from '@/components/SubscriptionManager';
import { PriceChart } from '@/components/PriceChart';
import { OrderBookDisplay } from '@/components/OrderBookDisplay';
import { WalletTracker } from '@/components/WalletTracker';
import { WalletAnalysis } from '@/components/WalletAnalysis';
import { LiveTradingFeed } from '@/components/LiveTradingFeed';
import { useHyperliquidWebSocket } from '@/hooks/useHyperliquidWebSocket';
import { useWalletTracker } from '@/hooks/useWalletTracker';
import { WalletData } from '@/types/hyperliquid';
import { Activity, TrendingUp, BookOpen, Settings, Wallet, Radio } from 'lucide-react';

export default function Home() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedWallet, setSelectedWallet] = useState<WalletData | null>(null);

  const {
    connectionStatus,
    subscriptions,
    priceData,
    orderBookData,
    subscribe,
    unsubscribe,
    connect,
    disconnect
  } = useHyperliquidWebSocket();

  const {
    wallets,
    walletTrades,
    liveActivity,
    addWallet,
    removeWallet,
    getWalletTrades,
    calculateScalpingMetrics
  } = useWalletTracker();

  const handleWalletClick = (wallet: WalletData) => {
    setSelectedWallet(wallet);
    setActiveTab('wallet-analysis');
  };

  const handleBackToWallets = () => {
    setSelectedWallet(null);
    setActiveTab('wallets');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                Hyperliquid Monitor
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                connectionStatus === 'connected'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : connectionStatus === 'connecting'
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}>
                {connectionStatus}
              </div>
              <button
                onClick={connectionStatus === 'connected' ? disconnect : connect}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {connectionStatus === 'connected' ? 'Disconnect' : 'Connect'}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white dark:bg-gray-800 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: TrendingUp },
              { id: 'live-feed', label: 'Live Feed', icon: Radio },
              { id: 'orderbook', label: 'Order Book', icon: BookOpen },
              { id: 'wallets', label: 'Wallet Tracker', icon: Wallet },
              { id: 'subscriptions', label: 'Subscriptions', icon: Settings },
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id)}
                className={`flex items-center px-3 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <PriceChart data={priceData} />
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
                  Active Subscriptions
                </h3>
                <div className="space-y-2">
                  {subscriptions.length === 0 ? (
                    <p className="text-gray-500 dark:text-gray-400">No active subscriptions</p>
                  ) : (
                    subscriptions.map((sub, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                        <span className="font-medium text-gray-900 dark:text-white">{sub.type}</span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">{sub.symbol}</span>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'live-feed' && (
          <LiveTradingFeed
            wallets={wallets}
            liveActivity={liveActivity}
            recentTrades={walletTrades.slice(0, 20)}
            onWalletClick={handleWalletClick}
          />
        )}

        {activeTab === 'orderbook' && (
          <OrderBookDisplay data={orderBookData} />
        )}

        {activeTab === 'wallets' && (
          <WalletTracker
            wallets={wallets}
            walletTrades={walletTrades}
            onAddWallet={addWallet}
            onRemoveWallet={removeWallet}
            onWalletClick={handleWalletClick}
          />
        )}

        {activeTab === 'wallet-analysis' && selectedWallet && (
          <WalletAnalysis
            wallet={selectedWallet}
            trades={getWalletTrades(selectedWallet.address)}
            scalpingMetrics={calculateScalpingMetrics(selectedWallet.address)}
            onBack={handleBackToWallets}
          />
        )}

        {activeTab === 'subscriptions' && (
          <SubscriptionManager
            subscriptions={subscriptions}
            onSubscribe={subscribe}
            onUnsubscribe={unsubscribe}
            connectionStatus={connectionStatus}
          />
        )}
      </main>
    </div>
  );
}
